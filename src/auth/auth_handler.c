/**
 * @file auth_handler.c
 * @brief 认证管理模块实现
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#include "auth_handler.h"
#include "api_router.h"
#include "file_utils.h"

/** MD5实现 - 严格复用旧项目deprecated/cgi/src/md5.c的实现 **/

#define F1(x, y, z) (z ^ (x & (y ^ z)))
#define F2(x, y, z) F1(z, x, y)
#define F3(x, y, z) (x ^ y ^ z)
#define F4(x, y, z) (y ^ (x | ~z))

#define MD5STEP(f, w, x, y, z, data, s) \
    ( w += f(x, y, z) + data,  w = w<<s | w>>(32-s),  w += x )

/**
 * @brief MD5初始化
 */
void MD5Init(struct MD5Context *ctx) {
    ctx->buf[0] = 0x67452301;
    ctx->buf[1] = 0xefcdab89;
    ctx->buf[2] = 0x98badcfe;
    ctx->buf[3] = 0x10325476;

    ctx->bits[0] = 0;
    ctx->bits[1] = 0;
}

/**
 * @brief MD5变换
 */
static void MD5Transform(uint32_t buf[4], uint32_t const in[16]) {
    register uint32_t a, b, c, d;

    a = buf[0];
    b = buf[1];
    c = buf[2];
    d = buf[3];

    MD5STEP(F1, a, b, c, d, in[0] + 0xd76aa478, 7);
    MD5STEP(F1, d, a, b, c, in[1] + 0xe8c7b756, 12);
    MD5STEP(F1, c, d, a, b, in[2] + 0x242070db, 17);
    MD5STEP(F1, b, c, d, a, in[3] + 0xc1bdceee, 22);
    MD5STEP(F1, a, b, c, d, in[4] + 0xf57c0faf, 7);
    MD5STEP(F1, d, a, b, c, in[5] + 0x4787c62a, 12);
    MD5STEP(F1, c, d, a, b, in[6] + 0xa8304613, 17);
    MD5STEP(F1, b, c, d, a, in[7] + 0xfd469501, 22);
    MD5STEP(F1, a, b, c, d, in[8] + 0x698098d8, 7);
    MD5STEP(F1, d, a, b, c, in[9] + 0x8b44f7af, 12);
    MD5STEP(F1, c, d, a, b, in[10] + 0xffff5bb1, 17);
    MD5STEP(F1, b, c, d, a, in[11] + 0x895cd7be, 22);
    MD5STEP(F1, a, b, c, d, in[12] + 0x6b901122, 7);
    MD5STEP(F1, d, a, b, c, in[13] + 0xfd987193, 12);
    MD5STEP(F1, c, d, a, b, in[14] + 0xa679438e, 17);
    MD5STEP(F1, b, c, d, a, in[15] + 0x49b40821, 22);

    MD5STEP(F2, a, b, c, d, in[1] + 0xf61e2562, 5);
    MD5STEP(F2, d, a, b, c, in[6] + 0xc040b340, 9);
    MD5STEP(F2, c, d, a, b, in[11] + 0x265e5a51, 14);
    MD5STEP(F2, b, c, d, a, in[0] + 0xe9b6c7aa, 20);
    MD5STEP(F2, a, b, c, d, in[5] + 0xd62f105d, 5);
    MD5STEP(F2, d, a, b, c, in[10] + 0x02441453, 9);
    MD5STEP(F2, c, d, a, b, in[15] + 0xd8a1e681, 14);
    MD5STEP(F2, b, c, d, a, in[4] + 0xe7d3fbc8, 20);
    MD5STEP(F2, a, b, c, d, in[9] + 0x21e1cde6, 5);
    MD5STEP(F2, d, a, b, c, in[14] + 0xc33707d6, 9);
    MD5STEP(F2, c, d, a, b, in[3] + 0xf4d50d87, 14);
    MD5STEP(F2, b, c, d, a, in[8] + 0x455a14ed, 20);
    MD5STEP(F2, a, b, c, d, in[13] + 0xa9e3e905, 5);
    MD5STEP(F2, d, a, b, c, in[2] + 0xfcefa3f8, 9);
    MD5STEP(F2, c, d, a, b, in[7] + 0x676f02d9, 14);
    MD5STEP(F2, b, c, d, a, in[12] + 0x8d2a4c8a, 20);

    MD5STEP(F3, a, b, c, d, in[5] + 0xfffa3942, 4);
    MD5STEP(F3, d, a, b, c, in[8] + 0x8771f681, 11);
    MD5STEP(F3, c, d, a, b, in[11] + 0x6d9d6122, 16);
    MD5STEP(F3, b, c, d, a, in[14] + 0xfde5380c, 23);
    MD5STEP(F3, a, b, c, d, in[1] + 0xa4beea44, 4);
    MD5STEP(F3, d, a, b, c, in[4] + 0x4bdecfa9, 11);
    MD5STEP(F3, c, d, a, b, in[7] + 0xf6bb4b60, 16);
    MD5STEP(F3, b, c, d, a, in[10] + 0xbebfbc70, 23);
    MD5STEP(F3, a, b, c, d, in[13] + 0x289b7ec6, 4);
    MD5STEP(F3, d, a, b, c, in[0] + 0xeaa127fa, 11);
    MD5STEP(F3, c, d, a, b, in[3] + 0xd4ef3085, 16);
    MD5STEP(F3, b, c, d, a, in[6] + 0x04881d05, 23);
    MD5STEP(F3, a, b, c, d, in[9] + 0xd9d4d039, 4);
    MD5STEP(F3, d, a, b, c, in[12] + 0xe6db99e5, 11);
    MD5STEP(F3, c, d, a, b, in[15] + 0x1fa27cf8, 16);
    MD5STEP(F3, b, c, d, a, in[2] + 0xc4ac5665, 23);

    MD5STEP(F4, a, b, c, d, in[0] + 0xf4292244, 6);
    MD5STEP(F4, d, a, b, c, in[7] + 0x432aff97, 10);
    MD5STEP(F4, c, d, a, b, in[14] + 0xab9423a7, 15);
    MD5STEP(F4, b, c, d, a, in[5] + 0xfc93a039, 21);
    MD5STEP(F4, a, b, c, d, in[12] + 0x655b59c3, 6);
    MD5STEP(F4, d, a, b, c, in[3] + 0x8f0ccc92, 10);
    MD5STEP(F4, c, d, a, b, in[10] + 0xffeff47d, 15);
    MD5STEP(F4, b, c, d, a, in[1] + 0x85845dd1, 21);
    MD5STEP(F4, a, b, c, d, in[8] + 0x6fa87e4f, 6);
    MD5STEP(F4, d, a, b, c, in[15] + 0xfe2ce6e0, 10);
    MD5STEP(F4, c, d, a, b, in[6] + 0xa3014314, 15);
    MD5STEP(F4, b, c, d, a, in[13] + 0x4e0811a1, 21);
    MD5STEP(F4, a, b, c, d, in[4] + 0xf7537e82, 6);
    MD5STEP(F4, d, a, b, c, in[11] + 0xbd3af235, 10);
    MD5STEP(F4, c, d, a, b, in[2] + 0x2ad7d2bb, 15);
    MD5STEP(F4, b, c, d, a, in[9] + 0xeb86d391, 21);

    buf[0] += a;
    buf[1] += b;
    buf[2] += c;
    buf[3] += d;
}

/**
 * @brief MD5更新
 */
void MD5Update(struct MD5Context *ctx, unsigned char const *buf, unsigned len) {
    uint32_t t;

    /* Update bitcount */
    t = ctx->bits[0];
    if ((ctx->bits[0] = t + ((uint32_t) len << 3)) < t)
        ctx->bits[1]++;         /* Carry from low to high */
    ctx->bits[1] += len >> 29;

    t = (t >> 3) & 0x3f;        /* Bytes already in shsInfo->data */

    /* Handle any leading odd-sized chunks */
    if (t) {
        unsigned char *p = (unsigned char *) ctx->in + t;

        t = 64 - t;
        if (len < t) {
            memcpy(p, buf, len);
            return;
        }
        memcpy(p, buf, t);
        MD5Transform(ctx->buf, (uint32_t *) ctx->in);
        buf += t;
        len -= t;
    }

    /* Process data in 64-byte chunks */
    while (len >= 64) {
        memcpy(ctx->in, buf, 64);
        MD5Transform(ctx->buf, (uint32_t *) ctx->in);
        buf += 64;
        len -= 64;
    }

    /* Handle any remaining bytes of data. */
    memcpy(ctx->in, buf, len);
}

/**
 * @brief MD5完成计算
 */
void MD5Final(unsigned char digest[16], struct MD5Context *ctx) {
    unsigned count;
    unsigned char *p;

    /* Compute number of bytes mod 64 */
    count = (ctx->bits[0] >> 3) & 0x3F;

    /* Set the first char of padding to 0x80.  This is safe since there is
       always at least one byte free */
    p = ctx->in + count;
    *p++ = 0x80;

    /* Bytes of padding needed to make 64 bytes */
    count = 64 - 1 - count;

    /* Pad out to 56 mod 64 */
    if (count < 8) {
        /* Two lots of padding:  Pad the first block to 64 bytes */
        memset(p, 0, count);
        MD5Transform(ctx->buf, (uint32_t *) ctx->in);

        /* Now fill the next block with 56 bytes */
        memset(ctx->in, 0, 56);
    } else {
        /* Pad block to 56 bytes */
        memset(p, 0, count - 8);
    }

    /* Append length in bits and transform */
    ((uint32_t *) ctx->in)[14] = ctx->bits[0];
    ((uint32_t *) ctx->in)[15] = ctx->bits[1];

    MD5Transform(ctx->buf, (uint32_t *) ctx->in);
    memcpy(digest, ctx->buf, 16);
    memset(ctx, 0, sizeof(*ctx));        /* In case it's sensitive */
}

/** Base64编码实现 - 严格复用旧项目的实现 **/

static char base64chars[64] = "abcdefghijklmnopqrstuvwxyz"
                              "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789./";

/**
 * @brief Base64编码
 */
void base64encode(unsigned char *from, char *to, int len) {
    while (len) {
        unsigned long k;
        int c;

        c = (len < 3) ? len : 3;
        k = 0;
        len -= c;
        while (c--)
            k = (k << 8) | *from++;

        *to++ = base64chars[(k >> 18) & 0x3f];
        *to++ = base64chars[(k >> 12) & 0x3f];
        *to++ = base64chars[(k >> 6) & 0x3f];
        *to++ = base64chars[k & 0x3f];
    }

    *to++ = 0;
}

/**
 * @brief 生成认证文件条目 - 严格复用旧项目实现
 */
void get_authfile(const char *user, const char *pass, FILE *fp) {
    struct MD5Context mc;
    unsigned char final[16];
    char encoded_passwd[0x40];

    MD5Init(&mc);
    MD5Update(&mc, (unsigned char *)pass, strlen(pass));
    MD5Final(final, &mc);
    strcpy(encoded_passwd, "$1$");
    base64encode(final, encoded_passwd + 3, 16);

    fprintf(fp, "%s:%s\n", user, encoded_passwd);
}

/**
 * @brief 验证密码格式
 */
int auth_utils_validate_password(const char *password) {
    if (!password) {
        return -1;
    }

    size_t len = strlen(password);
    if (len < 1 || len >= 32) {
        return -1; // 密码长度不合法
    }

    return 0; // 有效
}

/**
 * @brief 处理密码修改API
 * 严格对应旧项目0passwd.c的功能：
 * - 接收两个密码字段
 * - 验证密码是否匹配
 * - 写入到/etc/boa/login.passwd文件
 * - 返回对应的状态码
 */
int handle_auth_password(struct MHD_Connection *connection,
                        const char *url,
                        const char *method,
                        const char *upload_data,
                        size_t *upload_data_size,
                        void **con_cls) {
    (void)url; // 未使用的参数

    if (strcmp(method, "POST") != 0) {
        api_response_t *response = api_response_create_error(405, "Only POST method allowed");
        enum MHD_Result result = api_response_send(connection, response);
        api_response_free(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 获取POST数据
    cJSON *json_data = api_request_get_json_data(connection, upload_data, upload_data_size, con_cls);
    if (!json_data) {
        // 第一次调用或数据还在接收中
        return 0;
    }

    int result_code = 0;
    const char *error_message = NULL;

    do {
        // 解析JSON数据
        cJSON *new_password_item = cJSON_GetObjectItem(json_data, "new_password");
        cJSON *confirm_password_item = cJSON_GetObjectItem(json_data, "confirm_password");

        if (!new_password_item || !cJSON_IsString(new_password_item) ||
            !confirm_password_item || !cJSON_IsString(confirm_password_item)) {
            result_code = 1;
            error_message = "密码字段缺失或格式错误";
            break;
        }

        const char *new_password = new_password_item->valuestring;
        const char *confirm_password = confirm_password_item->valuestring;

        // 验证密码格式
        if (auth_utils_validate_password(new_password) != 0) {
            result_code = 1;
            error_message = "密码格式无效";
            break;
        }

        // 检查两次密码是否一致 - 对应旧项目的strcmp(cpswd1, cpswd2)
        if (strcmp(new_password, confirm_password) != 0) {
            result_code = 1;
            error_message = "两次输入的密码不匹配";
            break;
        }

        // 写入密码文件 - 对应旧项目的文件操作
        FILE *fp = fopen("/etc/boa/login.passwd", "w");
        if (!fp) {
            result_code = 2;
            error_message = "无法打开密码文件";
            break;
        }

        // 生成认证文件条目 - 复用旧项目的get_authfile函数
        get_authfile("admin", new_password, fp);
        fclose(fp);

        result_code = 3; // 成功
        error_message = "密码修改成功";

    } while (0);

    // 创建响应 - 对应旧项目的不同返回状态
    cJSON *response_json = cJSON_CreateObject();
    cJSON *data_json = cJSON_CreateObject();

    switch (result_code) {
        case 0:
            cJSON_AddStringToObject(response_json, "message", "请提交密码修改请求");
            cJSON_AddNumberToObject(response_json, "code", 200);
            break;
        case 1:
            cJSON_AddStringToObject(response_json, "message", error_message);
            cJSON_AddNumberToObject(response_json, "code", 400);
            break;
        case 2:
            cJSON_AddStringToObject(response_json, "message", "密码文件操作失败");
            cJSON_AddNumberToObject(response_json, "code", 500);
            break;
        case 3:
            cJSON_AddStringToObject(response_json, "message", "密码修改成功");
            cJSON_AddNumberToObject(response_json, "code", 200);
            break;
    }

    cJSON_AddNumberToObject(data_json, "status", result_code);
    cJSON_AddItemToObject(response_json, "data", data_json);

    api_response_t *response = api_response_create_success(response_json);
    enum MHD_Result mhd_result = api_response_send(connection, response);

    // 清理资源
    cJSON_Delete(json_data);
    cJSON_Delete(response_json);
    api_response_free(response);

    return (mhd_result == MHD_YES) ? 0 : -1;
} 