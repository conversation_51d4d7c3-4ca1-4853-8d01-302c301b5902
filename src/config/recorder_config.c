/**
 * @file recorder_config.c
 * @brief 录音模块配置管理实现 - 对应旧项目0recorder.c（简化版）
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#include "recorder_config.h"
#include "config_interface.h"
#include "file_utils.h"
#include "network_utils.h"
#include "api_router.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <arpa/inet.h>

// 静态变量（按命名规范）
static config_module_t s_recorder_module;

/**
 * @brief 设置录音配置默认值
 */
void recorder_config_set_default(cfg_recorder_t *config) {
    if (!config) return;
    
    memset(config, 0, sizeof(cfg_recorder_t));
    config->server_ip = inet_addr("***********");
    config->server_port = RECORDER_DEFAULT_SERVER_PORT;
    config->local_ip = inet_addr("*************");
    config->local_port = RECORDER_DEFAULT_LOCAL_PORT;
    config->enable = 1;
    config->device_type = 0x13;  // 默认录音模块
    config->timeout = RECORDER_DEFAULT_TIMEOUT;
}

/**
 * @brief 加载录音配置
 */
int recorder_config_load(cfg_recorder_t *config) {
    if (!config) return -1;
    
    // 使用公共工具模块读取二进制配置文件
    if (file_utils_read_binary(RECORDER_CONFIG_FILE, 0, sizeof(cfg_recorder_t), config) != 0) {
        printf("Warning: Failed to read recorder config, using defaults\n");
        recorder_config_set_default(config);
        return 0; // 使用默认配置不算错误
    }
    
    return 0;
}

/**
 * @brief 保存录音配置
 */
int recorder_config_save(const cfg_recorder_t *config) {
    if (!config) return -1;
    
    // 验证配置
    if (recorder_config_validate(config) != 0) {
        return -1;
    }
    
    // 使用公共工具模块写入二进制配置文件
    return file_utils_write_binary(RECORDER_CONFIG_FILE, 0, sizeof(cfg_recorder_t), config);
}

/**
 * @brief 验证录音配置
 */
int recorder_config_validate(const cfg_recorder_t *config) {
    if (!config) return -1;
    
    // 验证端口范围
    if (config->server_port == 0) {
        printf("Invalid recorder server port: %d\n", config->server_port);
        return -1;
    }

    if (config->local_port == 0) {
        printf("Invalid recorder local port: %d\n", config->local_port);
        return -1;
    }
    
    // 验证设备类型（录音模块0x13或最小基站0x17）
    if (config->device_type != 0x13 && config->device_type != 0x17) {
        printf("Invalid device type: 0x%02x\n", config->device_type);
        return -1;
    }
    
    return 0;
}

/**
 * @brief 录音配置转JSON
 */
int recorder_config_to_json(const cfg_recorder_t *config, cJSON **json) {
    if (!config || !json) return -1;
    
    *json = cJSON_CreateObject();
    if (!*json) return -1;
    
    // 使用公共工具模块转换IP地址为字符串
    char ip_str[16];
    
    if (ip_utils_binary_to_string(config->server_ip, ip_str, sizeof(ip_str)) == 0) {
        cJSON_AddStringToObject(*json, "server_ip", ip_str);
    }
    
    if (ip_utils_binary_to_string(config->local_ip, ip_str, sizeof(ip_str)) == 0) {
        cJSON_AddStringToObject(*json, "local_ip", ip_str);
    }
    
    // 添加其他基础字段
    cJSON_AddNumberToObject(*json, "server_port", config->server_port);
    cJSON_AddNumberToObject(*json, "local_port", config->local_port);
    cJSON_AddBoolToObject(*json, "enable", config->enable);
    cJSON_AddNumberToObject(*json, "device_type", config->device_type);
    cJSON_AddNumberToObject(*json, "timeout", config->timeout);
    
    return 0;
}

/**
 * @brief JSON转录音配置
 */
int recorder_json_to_config(const cJSON *json, cfg_recorder_t *config) {
    if (!json || !config) return -1;
    
    // 先设置默认值
    recorder_config_set_default(config);
    
    // 解析JSON字段
    cJSON *item;
    
    item = cJSON_GetObjectItem(json, "server_ip");
    if (item && cJSON_IsString(item)) {
        if (ip_utils_string_to_binary(item->valuestring, &config->server_ip) != 0) {
            return -1;
        }
    }
    
    item = cJSON_GetObjectItem(json, "local_ip");
    if (item && cJSON_IsString(item)) {
        if (ip_utils_string_to_binary(item->valuestring, &config->local_ip) != 0) {
            return -1;
        }
    }
    
    item = cJSON_GetObjectItem(json, "server_port");
    if (item && cJSON_IsNumber(item)) {
        config->server_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "local_port");
    if (item && cJSON_IsNumber(item)) {
        config->local_port = (uint16_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "enable");
    if (item && cJSON_IsBool(item)) {
        config->enable = cJSON_IsTrue(item) ? 1 : 0;
    }
    
    item = cJSON_GetObjectItem(json, "device_type");
    if (item && cJSON_IsNumber(item)) {
        config->device_type = (uint8_t)item->valueint;
    }
    
    item = cJSON_GetObjectItem(json, "timeout");
    if (item && cJSON_IsNumber(item)) {
        config->timeout = (uint16_t)item->valueint;
    }
    
    return 0;
}

// 配置管理器接口实现（按命名规范）
int recorder_module_load(void *config) {
    return recorder_config_load((cfg_recorder_t *)config);
}

int recorder_module_save(const void *config) {
    return recorder_config_save((const cfg_recorder_t *)config);
}

int recorder_module_validate(const void *config) {
    return recorder_config_validate((const cfg_recorder_t *)config);
}

int recorder_module_to_json(const void *config, cJSON **json) {
    return recorder_config_to_json((const cfg_recorder_t *)config, json);
}

int recorder_module_from_json(const cJSON *json, void *config) {
    return recorder_json_to_config(json, (cfg_recorder_t *)config);
}

// API处理函数（按命名规范）
/**
 * @brief 获取录音配置API处理函数
 */
int handle_recorder_get(struct MHD_Connection *connection,
                       const char *url,
                       const char *method,
                       const char *upload_data,
                       size_t *upload_data_size,
                       void **con_cls) {
    (void)url; (void)method; (void)upload_data; (void)upload_data_size; (void)con_cls;

    cfg_recorder_t config;
    cJSON *response_json = NULL;

    // 加载配置
    if (recorder_config_load(&config) != 0) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to load recorder configuration");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 转换为JSON
    if (recorder_config_to_json(&config, &response_json) != 0) {
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to convert recorder config to JSON");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 发送成功响应
    char *json_str = cJSON_Print(response_json);
    struct MHD_Response *response = MHD_create_response_from_buffer(
        strlen(json_str), json_str, MHD_RESPMEM_MUST_COPY);
    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    enum MHD_Result result = MHD_queue_response(connection, 200, response);

    free(json_str);
    cJSON_Delete(response_json);
    MHD_destroy_response(response);
    return (result == MHD_YES) ? 0 : -1;
}

/**
 * @brief 设置录音配置API处理函数
 */
int handle_recorder_post(struct MHD_Connection *connection,
                        const char *url,
                        const char *method,
                        const char *upload_data,
                        size_t *upload_data_size,
                        void **con_cls) {
    (void)url; (void)method;

    // 获取POST数据
    cJSON *request_data = api_request_get_json_data(connection, upload_data, upload_data_size, con_cls);
    if (!request_data) {
        if (*upload_data_size == 0) {
            // 发送错误响应
            cJSON *error_json = cJSON_CreateObject();
            cJSON_AddStringToObject(error_json, "error", "No JSON data provided");
            char *error_str = cJSON_Print(error_json);

            struct MHD_Response *response = MHD_create_response_from_buffer(
                strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
            MHD_add_response_header(response, "Content-Type", "application/json");
            enum MHD_Result result = MHD_queue_response(connection, 400, response);

            free(error_str);
            cJSON_Delete(error_json);
            MHD_destroy_response(response);
            return (result == MHD_YES) ? 0 : -1;
        }
        return MHD_YES; // 继续等待数据
    }

    cfg_recorder_t config;

    // JSON转配置结构
    if (recorder_json_to_config(request_data, &config) != 0) {
        cJSON_Delete(request_data);
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Invalid recorder configuration data");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 400, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 保存配置
    if (recorder_config_save(&config) != 0) {
        cJSON_Delete(request_data);
        // 发送错误响应
        cJSON *error_json = cJSON_CreateObject();
        cJSON_AddStringToObject(error_json, "error", "Failed to save recorder configuration");
        char *error_str = cJSON_Print(error_json);

        struct MHD_Response *response = MHD_create_response_from_buffer(
            strlen(error_str), error_str, MHD_RESPMEM_MUST_COPY);
        MHD_add_response_header(response, "Content-Type", "application/json");
        enum MHD_Result result = MHD_queue_response(connection, 500, response);

        free(error_str);
        cJSON_Delete(error_json);
        MHD_destroy_response(response);
        return (result == MHD_YES) ? 0 : -1;
    }

    // 返回成功响应
    cJSON *response_data = cJSON_CreateObject();
    cJSON_AddStringToObject(response_data, "message", "Recorder configuration saved successfully");
    char *json_str = cJSON_Print(response_data);

    struct MHD_Response *response = MHD_create_response_from_buffer(
        strlen(json_str), json_str, MHD_RESPMEM_MUST_COPY);
    MHD_add_response_header(response, "Content-Type", "application/json");
    MHD_add_response_header(response, "Access-Control-Allow-Origin", "*");
    enum MHD_Result result = MHD_queue_response(connection, 200, response);

    free(json_str);
    cJSON_Delete(response_data);
    cJSON_Delete(request_data);
    MHD_destroy_response(response);
    return (result == MHD_YES) ? 0 : -1;
}

/**
 * @brief 初始化录音配置模块
 */
int recorder_config_init(void) {
    // 初始化配置模块结构
    s_recorder_module.module_name = "recorder";
    s_recorder_module.config_file_path = RECORDER_CONFIG_FILE;
    s_recorder_module.config_struct_size = sizeof(cfg_recorder_t);
    s_recorder_module.load_config = recorder_module_load;
    s_recorder_module.save_config = recorder_module_save;
    s_recorder_module.validate_config = recorder_module_validate;
    s_recorder_module.config_to_json = recorder_module_to_json;
    s_recorder_module.json_to_config = recorder_module_from_json;

    // 注册到配置管理器
    if (config_manager_register(&s_recorder_module) != 0) {
        printf("Failed to register recorder config module\n");
        return -1;
    }

    printf("Recorder config module initialized\n");
    return 0;
}

/**
 * @brief 清理录音配置模块
 */
void recorder_config_cleanup(void) {
    printf("Recorder config module cleanup\n");
}
