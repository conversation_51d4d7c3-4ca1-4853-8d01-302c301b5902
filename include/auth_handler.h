/**
 * @file auth_handler.h
 * @brief 认证管理模块
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0
 */

#ifndef AUTH_HANDLER_H
#define AUTH_HANDLER_H

#include <stdio.h>
#include <stdint.h>
#include <microhttpd.h>
#include <cjson/cJSON.h>

/**
 * @brief MD5上下文结构体
 */
struct MD5Context {
    uint32_t buf[4];
    uint32_t bits[2];
    unsigned char in[64];
};

/**
 * @brief 密码修改请求结构体
 */
typedef struct {
    char new_password[32];      // 新密码
    char confirm_password[32];  // 确认密码
} auth_password_request_t;

/**
 * @brief 处理密码修改API
 * @param connection HTTP连接
 * @param url 请求URL
 * @param method HTTP方法
 * @param upload_data 上传数据
 * @param upload_data_size 上传数据大小
 * @param con_cls 连接状态
 * @return 0成功，-1失败
 */
int handle_auth_password(struct MHD_Connection *connection,
                        const char *url,
                        const char *method,
                        const char *upload_data,
                        size_t *upload_data_size,
                        void **con_cls);

/**
 * @brief MD5初始化
 * @param context MD5上下文
 */
void MD5Init(struct MD5Context *context);

/**
 * @brief MD5更新
 * @param context MD5上下文
 * @param buf 数据缓冲区
 * @param len 数据长度
 */
void MD5Update(struct MD5Context *context, unsigned char const *buf, unsigned len);

/**
 * @brief MD5完成计算
 * @param digest 输出摘要
 * @param context MD5上下文
 */
void MD5Final(unsigned char digest[16], struct MD5Context *context);

/**
 * @brief Base64编码
 * @param from 输入数据
 * @param to 输出缓冲区
 * @param len 输入数据长度
 */
void base64encode(unsigned char *from, char *to, int len);

/**
 * @brief 生成认证文件条目
 * @param user 用户名
 * @param pass 密码
 * @param fp 文件指针
 */
void get_authfile(const char *user, const char *pass, FILE *fp);

/**
 * @brief 验证密码格式
 * @param password 密码
 * @return 0有效，-1无效
 */
int auth_utils_validate_password(const char *password);

#endif // AUTH_HANDLER_H 