<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS框架测试 - VICTEL IP交换机配置系统</title>
    <link rel="stylesheet" href="../css/app.css">
    <link rel="stylesheet" href="../css/components.css">
    <link rel="stylesheet" href="../css/responsive.css">
</head>
<body>
    <div class="app-layout">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <div class="header-brand">
                <button class="mobile-menu-toggle" aria-label="切换菜单">
                    ☰
                </button>
                <img src="../assets/logo.svg" alt="VICTEL" class="logo">
                <h1>CSS框架测试</h1>
            </div>
            <div class="header-user">
                <span class="status-indicator">
                    <span class="status-dot online"></span>
                    <span>系统正常</span>
                </span>
            </div>
        </header>
        
        <!-- 主要内容区域 -->
        <div class="app-container">
            <!-- 侧边栏导航 -->
            <aside class="app-sidebar">
                <nav class="nav">
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#" class="nav-link active">
                                <span class="nav-icon">🏠</span>
                                <span class="nav-text">首页</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link">
                                <span class="nav-icon">⚙️</span>
                                <span class="nav-text">设备配置</span>
                            </a>
                        </li>
                    </ul>
                </nav>
            </aside>
            
            <!-- 主内容区域 -->
            <main class="app-main">
                <div class="main-content">
                    <div class="breadcrumb">
                        <span>首页</span> > <span>CSS框架测试</span>
                    </div>
                    
                    <!-- 系统状态 -->
                    <div class="system-status">
                        <div class="status-card status-good">
                            <div class="status-value">正常</div>
                            <div class="status-label">系统状态</div>
                            <div class="status-description">所有服务运行正常</div>
                        </div>
                        <div class="status-card status-warning">
                            <div class="status-value">85%</div>
                            <div class="status-label">CPU使用率</div>
                            <div class="status-description">当前负载较高</div>
                        </div>
                        <div class="status-card status-good">
                            <div class="status-value">64GB</div>
                            <div class="status-label">可用内存</div>
                            <div class="status-description">内存充足</div>
                        </div>
                    </div>
                    
                    <!-- 表单测试 -->
                    <div class="card">
                        <div class="card-header">
                            <h3>表单网格测试</h3>
                        </div>
                        <div class="card-body">
                            <form class="config-form">
                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label required">设备名称</label>
                                        <input type="text" class="form-control" placeholder="输入设备名称">
                                        <small class="form-help">设备的唯一标识名称</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">设备描述</label>
                                        <input type="text" class="form-control" placeholder="输入设备描述">
                                        <small class="form-help">设备的详细描述信息</small>
                                    </div>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-col-4">
                                        <label class="form-label">端口</label>
                                        <input type="number" class="form-control" placeholder="8080">
                                    </div>
                                    <div class="form-col-8">
                                        <label class="form-label">IP地址</label>
                                        <input type="text" class="form-control" placeholder="***********">
                                    </div>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="button" class="btn btn-secondary">取消</button>
                                    <button type="submit" class="btn btn-primary">保存配置</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- 按钮测试 -->
                    <div class="card">
                        <div class="card-header">
                            <h3>按钮样式测试</h3>
                        </div>
                        <div class="card-body">
                            <div style="display: flex; gap: 1rem; flex-wrap: wrap; margin-bottom: 1rem;">
                                <button class="btn btn-primary">主要按钮</button>
                                <button class="btn btn-secondary">次要按钮</button>
                                <button class="btn btn-success">成功按钮</button>
                                <button class="btn btn-danger">危险按钮</button>
                                <button class="btn btn-warning">警告按钮</button>
                            </div>
                            
                            <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                                <span class="badge badge-primary">主要标签</span>
                                <span class="badge badge-success">成功标签</span>
                                <span class="badge badge-danger">危险标签</span>
                                <span class="badge badge-warning">警告标签</span>
                                <span class="badge badge-info">信息标签</span>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="../js/utils.js"></script>
    <script src="../js/app.js"></script>
</body>
</html>
