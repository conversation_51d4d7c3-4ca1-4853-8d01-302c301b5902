/**
 * 网络选择配置页面
 * 严格按照重构设计方案实现，使用公共工具模块减少冗余代码
 */

const NetworkSelectionPage = {
    // 页面配置
    config: {
        title: '网络选择配置',
        apiEndpoint: '/config/network/selection'
    },

    // 当前配置数据
    currentConfig: null,

    /**
     * 渲染页面内容
     * @returns {string} HTML内容
     */
    render: function() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h2>${this.config.title}</h2>
                    <div class="page-actions">
                        <button type="button" class="btn btn-secondary" data-action="refresh">
                            <i class="icon-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>网络接口选择</h3>
                            <p class="card-description">选择系统使用的网络接口类型</p>
                        </div>
                        
                        <div class="card-body">
                            <form id="network-selection-form" class="config-form">
                                <div class="form-group">
                                    <label class="form-label required">网络接口类型</label>
                                    <div class="radio-group">
                                        <label class="radio-item">
                                            <input type="radio" name="network_type" value="0" required>
                                            <span class="radio-label">以太网接口</span>
                                            <small class="form-help">使用有线以太网连接</small>
                                        </label>
                                        <label class="radio-item">
                                            <input type="radio" name="network_type" value="1" required>
                                            <span class="radio-label">3G/4G模块</span>
                                            <small class="form-help">使用移动网络连接</small>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group" id="ethernet-options" style="display: none;">
                                    <label class="form-label">以太网配置模式</label>
                                    <div class="radio-group">
                                        <label class="radio-item">
                                            <input type="radio" name="ethernet_mode" value="0">
                                            <span class="radio-label">DHCP自动获取</span>
                                            <small class="form-help">自动获取IP地址和网络配置</small>
                                        </label>
                                        <label class="radio-item">
                                            <input type="radio" name="ethernet_mode" value="1">
                                            <span class="radio-label">静态IP配置</span>
                                            <small class="form-help">手动设置IP地址和网络参数</small>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group" id="mobile-options" style="display: none;">
                                    <label class="form-label">移动网络配置</label>
                                    <div class="form-row">
                                        <div class="form-col">
                                            <label class="form-label">APN接入点</label>
                                            <input type="text" name="apn" class="form-control" 
                                                   placeholder="例如: cmnet" maxlength="32">
                                            <small class="form-help">移动网络接入点名称</small>
                                        </div>
                                        <div class="form-col">
                                            <label class="form-label">拨号号码</label>
                                            <input type="text" name="dial_number" class="form-control" 
                                                   placeholder="例如: *99#" maxlength="16">
                                            <small class="form-help">移动网络拨号号码</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">网络优先级</label>
                                    <select name="priority" class="form-control">
                                        <option value="0">高优先级</option>
                                        <option value="1">中优先级</option>
                                        <option value="2">低优先级</option>
                                    </select>
                                    <small class="form-help">设置网络连接的优先级别</small>
                                </div>

                                <div class="form-group">
                                    <div class="checkbox-item">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="auto_reconnect" value="1">
                                            <span class="checkbox-text">启用自动重连</span>
                                        </label>
                                        <small class="form-help">网络断开时自动尝试重新连接</small>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="icon-save"></i> 保存配置
                                    </button>
                                    <button type="button" class="btn btn-secondary" data-action="reset">
                                        <i class="icon-reset"></i> 重置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3>当前网络状态</h3>
                        </div>
                        <div class="card-body">
                            <div class="status-grid" id="network-status">
                                <div class="status-item">
                                    <span class="status-label">连接状态:</span>
                                    <span class="status-value" id="connection-status">检测中...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">当前IP:</span>
                                    <span class="status-value" id="current-ip">获取中...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">网络类型:</span>
                                    <span class="status-value" id="network-type">检测中...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">信号强度:</span>
                                    <span class="status-value" id="signal-strength">检测中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * 初始化页面
     */
    init: function() {
        this.bindEvents();
        this.loadConfig();
        this.updateNetworkStatus();
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        const form = Utils.dom.find('#network-selection-form');
        const networkTypeRadios = Utils.dom.findAll('input[name="network_type"]');
        
        // 表单提交事件
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveConfig();
        });

        // 网络类型切换事件
        networkTypeRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                this.toggleNetworkOptions();
            });
        });

        // 重置按钮事件
        const resetBtn = Utils.dom.find('[data-action="reset"]');
        resetBtn.addEventListener('click', () => {
            this.resetForm();
        });

        // 刷新按钮事件
        const refreshBtn = Utils.dom.find('[data-action="refresh"]');
        refreshBtn.addEventListener('click', () => {
            this.loadConfig();
            this.updateNetworkStatus();
        });
    },

    /**
     * 切换网络选项显示
     */
    toggleNetworkOptions: function() {
        const networkType = Utils.dom.find('input[name="network_type"]:checked');
        const ethernetOptions = Utils.dom.find('#ethernet-options');
        const mobileOptions = Utils.dom.find('#mobile-options');

        if (networkType) {
            if (networkType.value === '0') {
                // 以太网
                ethernetOptions.style.display = 'block';
                mobileOptions.style.display = 'none';
            } else {
                // 3G/4G
                ethernetOptions.style.display = 'none';
                mobileOptions.style.display = 'block';
            }
        }
    },

    /**
     * 加载配置数据
     */
    loadConfig: async function() {
        try {
            UI.showLoading();
            const response = await API.network.getSelection();
            
            if (response && response.data) {
                this.currentConfig = response.data;
                this.populateForm(response.data);
            }
        } catch (error) {
            UI.showNotification('加载配置失败: ' + error.message, 'error');
        } finally {
            UI.hideLoading();
        }
    },

    /**
     * 填充表单数据
     * @param {Object} config - 配置数据
     */
    populateForm: function(config) {
        // 设置网络类型
        const networkTypeRadio = Utils.dom.find(`input[name="network_type"][value="${config.network_type || 0}"]`);
        if (networkTypeRadio) {
            networkTypeRadio.checked = true;
            this.toggleNetworkOptions();
        }

        // 设置以太网模式
        if (config.ethernet_mode !== undefined) {
            const ethernetModeRadio = Utils.dom.find(`input[name="ethernet_mode"][value="${config.ethernet_mode}"]`);
            if (ethernetModeRadio) {
                ethernetModeRadio.checked = true;
            }
        }

        // 设置移动网络配置
        if (config.apn) {
            const apnInput = Utils.dom.find('input[name="apn"]');
            if (apnInput) apnInput.value = config.apn;
        }

        if (config.dial_number) {
            const dialInput = Utils.dom.find('input[name="dial_number"]');
            if (dialInput) dialInput.value = config.dial_number;
        }

        // 设置优先级
        const prioritySelect = Utils.dom.find('select[name="priority"]');
        if (prioritySelect && config.priority !== undefined) {
            prioritySelect.value = config.priority;
        }

        // 设置自动重连
        const autoReconnectCheckbox = Utils.dom.find('input[name="auto_reconnect"]');
        if (autoReconnectCheckbox) {
            autoReconnectCheckbox.checked = config.auto_reconnect === 1;
        }
    },

    /**
     * 保存配置
     */
    saveConfig: async function() {
        try {
            const formData = this.getFormData();
            
            // 验证表单数据
            if (!this.validateForm(formData)) {
                return;
            }

            UI.showLoading();
            const response = await API.network.saveSelection(formData);
            
            if (response && response.code === 200) {
                UI.showNotification('配置保存成功', 'success');
                this.currentConfig = formData;
            } else {
                throw new Error(response.message || '保存失败');
            }
        } catch (error) {
            UI.showNotification('保存配置失败: ' + error.message, 'error');
        } finally {
            UI.hideLoading();
        }
    },

    /**
     * 获取表单数据
     * @returns {Object} 表单数据
     */
    getFormData: function() {
        const form = Utils.dom.find('#network-selection-form');
        const formData = new FormData(form);
        
        return {
            network_type: parseInt(formData.get('network_type')) || 0,
            ethernet_mode: parseInt(formData.get('ethernet_mode')) || 0,
            apn: formData.get('apn') || '',
            dial_number: formData.get('dial_number') || '',
            priority: parseInt(formData.get('priority')) || 0,
            auto_reconnect: formData.get('auto_reconnect') ? 1 : 0
        };
    },

    /**
     * 验证表单数据
     * @param {Object} data - 表单数据
     * @returns {boolean} 验证结果
     */
    validateForm: function(data) {
        // 验证网络类型
        if (data.network_type === undefined || data.network_type < 0 || data.network_type > 1) {
            UI.showNotification('请选择有效的网络接口类型', 'warning');
            return false;
        }

        // 验证移动网络配置
        if (data.network_type === 1) {
            if (!data.apn || data.apn.trim() === '') {
                UI.showNotification('使用移动网络时必须设置APN接入点', 'warning');
                return false;
            }
        }

        return true;
    },

    /**
     * 重置表单
     */
    resetForm: function() {
        if (this.currentConfig) {
            this.populateForm(this.currentConfig);
        } else {
            const form = Utils.dom.find('#network-selection-form');
            form.reset();
            this.toggleNetworkOptions();
        }
    },

    /**
     * 更新网络状态
     */
    updateNetworkStatus: async function() {
        try {
            // 这里可以调用系统状态API获取实时网络状态
            // 暂时使用模拟数据
            const statusData = {
                connection_status: '已连接',
                current_ip: '*************',
                network_type: '以太网',
                signal_strength: 'N/A'
            };

            this.updateStatusDisplay(statusData);
        } catch (error) {
            console.error('更新网络状态失败:', error);
        }
    },

    /**
     * 更新状态显示
     * @param {Object} statusData - 状态数据
     */
    updateStatusDisplay: function(statusData) {
        const connectionStatus = Utils.dom.find('#connection-status');
        const currentIP = Utils.dom.find('#current-ip');
        const networkType = Utils.dom.find('#network-type');
        const signalStrength = Utils.dom.find('#signal-strength');

        if (connectionStatus) connectionStatus.textContent = statusData.connection_status;
        if (currentIP) currentIP.textContent = statusData.current_ip;
        if (networkType) networkType.textContent = statusData.network_type;
        if (signalStrength) signalStrength.textContent = statusData.signal_strength;
    }
};
