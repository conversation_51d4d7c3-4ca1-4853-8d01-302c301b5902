/**
 * 系统管理页面
 * 严格按照重构设计方案实现，仅保留旧项目0system.c中存在的功能
 * 移除系统配置、备份恢复、升级等旧项目中不存在的功能
 */

const SystemManagementPage = {
    // 页面配置
    config: {
        title: '系统管理',
        apiEndpoint: '/system'
    },

    /**
     * 渲染页面内容
     * @returns {string} HTML内容
     */
    render: function() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h2>${this.config.title}</h2>
                    <div class="page-actions">
                        <button type="button" class="btn btn-secondary" data-action="refresh">
                            <i class="icon-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>系统操作</h3>
                            <p class="card-description">执行系统基本操作（对应旧项目0system.c功能）</p>
                        </div>

                        <div class="card-body">
                            <div class="action-grid">
                                <div class="action-item">
                                    <div class="action-icon">
                                        <i class="icon-restart"></i>
                                    </div>
                                    <div class="action-content">
                                        <h4>重启系统</h4>
                                        <p>重新启动系统服务（对应0system.c的reboot功能）</p>
                                        <button type="button" class="btn btn-warning" data-action="restart">
                                            <i class="icon-restart"></i> 重启系统
                                        </button>
                                    </div>
                                </div>

                                <div class="action-item">
                                    <div class="action-icon">
                                        <i class="icon-reset"></i>
                                    </div>
                                    <div class="action-content">
                                        <h4>重置配置</h4>
                                        <p>重置系统配置到默认状态（对应0system.c的reset功能）</p>
                                        <button type="button" class="btn btn-danger" data-action="reset-config">
                                            <i class="icon-reset"></i> 重置配置
                                        </button>
                                    </div>
                                </div>

                                <div class="action-item">
                                    <div class="action-icon">
                                        <i class="icon-upload"></i>
                                    </div>
                                    <div class="action-content">
                                        <h4>文件上传</h4>
                                        <p>上传配置文件到系统（对应0system.c的upload功能）</p>
                                        <input type="file" id="upload-file" style="display: none;">
                                        <button type="button" class="btn btn-primary" data-action="upload">
                                            <i class="icon-upload"></i> 选择文件上传
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3>功能说明</h3>
                            <p class="card-description">系统管理功能对应关系</p>
                        </div>

                        <div class="card-body">
                            <div class="function-mapping">
                                <table class="mapping-table">
                                    <thead>
                                        <tr>
                                            <th>系统操作</th>
                                            <th>对应旧CGI</th>
                                            <th>功能描述</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>重启系统</td>
                                            <td>0system.c - reboot</td>
                                            <td>系统重启操作</td>
                                        </tr>
                                        <tr>
                                            <td>重置配置</td>
                                            <td>0system.c - reset</td>
                                            <td>配置重置操作</td>
                                        </tr>
                                        <tr>
                                            <td>文件上传</td>
                                            <td>0system.c - upload</td>
                                            <td>配置文件上传</td>
                                        </tr>
                                        <tr>
                                            <td>系统日志</td>
                                            <td>0down.c - 日志显示</td>
                                            <td>查看系统运行日志</td>
                                        </tr>
                                        <tr>
                                            <td>3G信号检测</td>
                                            <td>0down.c - 信号检测</td>
                                            <td>检测3G网络信号强度</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="function-note">
                                <h4>重要说明</h4>
                                <ul>
                                    <li>系统管理功能严格对应旧项目0system.c的3个基本操作</li>
                                    <li>移除了旧项目中不存在的系统关闭、备份恢复、升级等功能</li>
                                    <li>保持与旧项目100%功能兼容</li>
                                    <li>使用公共工具模块减少代码冗余</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * 初始化页面
     */
    init: function() {
        this.bindEvents();
        // 移除复杂的配置加载功能，严格遵循重构设计约束
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 刷新按钮事件 - 简化为页面重新加载
        const refreshBtn = Utils.dom.find('[data-action="refresh"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                location.reload();
            });
        }

        // 绑定系统操作事件（仅保留旧项目中存在的功能）
        this.bindSystemActions();
    },

    /**
     * 绑定系统操作事件
     * 仅保留对应0system.c中的3个基本操作
     */
    bindSystemActions: function() {
        // 重启系统（对应0system.c的reboot功能）
        const restartBtn = Utils.dom.find('[data-action="restart"]');
        if (restartBtn) {
            restartBtn.addEventListener('click', () => {
                this.confirmSystemAction('重启系统', '确定要重启系统吗？', () => {
                    this.restartSystem();
                });
            });
        }

        // 重置配置（对应0system.c的reset功能）
        const resetConfigBtn = Utils.dom.find('[data-action="reset-config"]');
        if (resetConfigBtn) {
            resetConfigBtn.addEventListener('click', () => {
                this.confirmSystemAction('重置配置', '确定要重置配置吗？', () => {
                    this.resetConfig();
                });
            });
        }

        // 文件上传（对应0system.c的upload功能）
        const uploadBtn = Utils.dom.find('[data-action="upload"]');
        const uploadFile = Utils.dom.find('#upload-file');
        if (uploadBtn && uploadFile) {
            uploadBtn.addEventListener('click', () => {
                uploadFile.click();
            });
            uploadFile.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    this.uploadFile(e.target.files[0]);
                }
            });
        }
    },

    /**
     * 确认系统操作
     * @param {string} title - 操作标题
     * @param {string} message - 确认消息
     * @param {Function} callback - 确认后的回调函数
     */
    confirmSystemAction: function(title, message, callback) {
        if (confirm(`${title}\n\n${message}`)) {
            callback();
        }
    },

    /**
     * 重启系统（对应0system.c的reboot功能）
     */

    restartSystem: async function() {
        try {
            if (typeof UI !== 'undefined') {
                UI.showLoading();
            }

            const response = await API.system.restart();

            if (response && response.code === 200) {
                const message = '系统重启命令已发送';
                if (typeof UI !== 'undefined') {
                    UI.showNotification(message, 'success');
                } else {
                    alert(message);
                }
            } else {
                throw new Error(response.message || '重启失败');
            }
        } catch (error) {
            const message = '重启系统失败: ' + error.message;
            if (typeof UI !== 'undefined') {
                UI.showNotification(message, 'error');
            } else {
                alert(message);
            }
        } finally {
            if (typeof UI !== 'undefined') {
                UI.hideLoading();
            }
        }
    },

    /**
     * 重置配置（对应0system.c的reset功能）
     */
    resetConfig: async function() {
        try {
            if (typeof UI !== 'undefined') {
                UI.showLoading();
            }

            const response = await API.system.reset();

            if (response && response.code === 200) {
                const message = '配置重置成功';
                if (typeof UI !== 'undefined') {
                    UI.showNotification(message, 'success');
                } else {
                    alert(message);
                }
            } else {
                throw new Error(response.message || '重置失败');
            }
        } catch (error) {
            const message = '重置配置失败: ' + error.message;
            if (typeof UI !== 'undefined') {
                UI.showNotification(message, 'error');
            } else {
                alert(message);
            }
        } finally {
            if (typeof UI !== 'undefined') {
                UI.hideLoading();
            }
        }
    },

    /**
     * 文件上传（对应0system.c的upload功能）
     * @param {File} file - 上传文件
     */
    uploadFile: async function(file) {
        try {
            if (!file) {
                const message = '请选择要上传的文件';
                if (typeof UI !== 'undefined') {
                    UI.showNotification(message, 'warning');
                } else {
                    alert(message);
                }
                return;
            }

            if (typeof UI !== 'undefined') {
                UI.showLoading();
            }

            const response = await API.upload('/system/upload', file);

            if (response && response.code === 200) {
                const message = '文件上传成功';
                if (typeof UI !== 'undefined') {
                    UI.showNotification(message, 'success');
                } else {
                    alert(message);
                }
            } else {
                throw new Error(response.message || '上传失败');
            }
        } catch (error) {
            const message = '文件上传失败: ' + error.message;
            if (typeof UI !== 'undefined') {
                UI.showNotification(message, 'error');
            } else {
                alert(message);
            }
        } finally {
            if (typeof UI !== 'undefined') {
                UI.hideLoading();
            }
        }
    }
};
