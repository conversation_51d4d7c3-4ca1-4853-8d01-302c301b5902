/**
 * 设备配置页面
 * 严格按照重构设计方案实现，仅保留旧项目中存在的功能
 * 移除设备状态监控、复杂模块配置等旧项目中不存在的功能
 */

const DeviceConfigPage = {
    // 页面配置
    config: {
        title: '设备配置',
        apiEndpoint: '/config/device'
    },

    // 当前配置数据
    currentConfig: null,

    /**
     * 渲染页面内容
     * @returns {string} HTML内容
     */
    render: function() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h2>${this.config.title}</h2>
                    <div class="page-actions">
                        <button type="button" class="btn btn-secondary" data-action="refresh">
                            <i class="icon-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>设备配置说明</h3>
                            <p class="card-description">设备配置功能已分散到各个专用配置模块中</p>
                        </div>

                        <div class="card-body">
                            <div class="info-grid">
                                <div class="info-item">
                                    <h4>网络配置</h4>
                                    <p>配置设备的网络连接参数，包括以太网和网络选择设置。</p>
                                    <button type="button" class="btn btn-primary" onclick="Router.navigate('network-selection')">
                                        <i class="icon-network"></i> 进入网络配置
                                    </button>
                                </div>

                                <div class="info-item">
                                    <h4>呼叫中心配置</h4>
                                    <p>配置呼叫中心模块的服务器连接和基本参数。</p>
                                    <button type="button" class="btn btn-primary" onclick="Router.navigate('center-config')">
                                        <i class="icon-center"></i> 进入呼叫中心配置
                                    </button>
                                </div>

                                <div class="info-item">
                                    <h4>网关配置</h4>
                                    <p>配置互联网关模块的连接参数和功能设置。</p>
                                    <button type="button" class="btn btn-primary" onclick="Router.navigate('gateway-config')">
                                        <i class="icon-gateway"></i> 进入网关配置
                                    </button>
                                </div>

                                <div class="info-item">
                                    <h4>录音配置</h4>
                                    <p>配置录音模块和迷你基站的基本参数。</p>
                                    <button type="button" class="btn btn-primary" onclick="Router.navigate('recorder-config')">
                                        <i class="icon-recorder"></i> 进入录音配置
                                    </button>
                                </div>

                                <div class="info-item">
                                    <h4>基站配置</h4>
                                    <p>配置SCI基站模块的连接和通信参数。</p>
                                    <button type="button" class="btn btn-primary" onclick="Router.navigate('sci-config')">
                                        <i class="icon-sci"></i> 进入基站配置
                                    </button>
                                </div>

                                <div class="info-item">
                                    <h4>交换机配置</h4>
                                    <p>配置专用交换机模块的基本连接参数。</p>
                                    <button type="button" class="btn btn-primary" onclick="Router.navigate('switch-config')">
                                        <i class="icon-switch"></i> 进入交换机配置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3>配置模块说明</h3>
                            <p class="card-description">各配置模块对应旧项目中的CGI程序功能</p>
                        </div>

                        <div class="card-body">
                            <div class="module-info">
                                <div class="module-mapping">
                                    <h4>配置模块映射关系</h4>
                                    <table class="mapping-table">
                                        <thead>
                                            <tr>
                                                <th>配置模块</th>
                                                <th>对应旧CGI程序</th>
                                                <th>功能描述</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>网络配置</td>
                                                <td>网络设置文件</td>
                                                <td>以太网、3G网络配置</td>
                                            </tr>
                                            <tr>
                                                <td>呼叫中心配置</td>
                                                <td>0center.c</td>
                                                <td>呼叫中心基础配置读写</td>
                                            </tr>
                                            <tr>
                                                <td>网关配置</td>
                                                <td>0gateway.c</td>
                                                <td>互联网关基础配置读写</td>
                                            </tr>
                                            <tr>
                                                <td>录音配置</td>
                                                <td>0recorder.c / 0mini.c</td>
                                                <td>录音模块/迷你基站配置</td>
                                            </tr>
                                            <tr>
                                                <td>基站配置</td>
                                                <td>0sci*.c</td>
                                                <td>SCI基站基础配置读写</td>
                                            </tr>
                                            <tr>
                                                <td>交换机配置</td>
                                                <td>0switch*.c</td>
                                                <td>专用交换机基础配置读写</td>
                                            </tr>
                                            <tr>
                                                <td>NTP配置</td>
                                                <td>0ntp.c</td>
                                                <td>时间同步配置（74行代码）</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="module-note">
                                    <h4>重要说明</h4>
                                    <ul>
                                        <li>所有配置模块严格对应旧项目中的CGI程序功能</li>
                                        <li>不包含旧项目中不存在的复杂业务逻辑</li>
                                        <li>保持配置文件格式100%兼容</li>
                                        <li>使用公共工具模块减少代码冗余</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * 初始化页面
     */
    init: function() {
        this.bindEvents();
        // 移除复杂的配置加载和状态监控功能
        // 严格遵循重构设计约束，仅保留导航功能
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        // 刷新按钮事件 - 简化为页面重新加载
        const refreshBtn = Utils.dom.find('[data-action="refresh"]');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
                location.reload();
            });
        }

        // 使用公共工具模块处理导航事件
        Utils.navigation.bindNavigationEvents();
    },

    /**
     * 显示配置说明信息
     * 使用公共工具模块显示统一的信息提示
     */
    showConfigInfo: function() {
        const infoMessage = `
            设备配置功能已按照重构设计方案进行模块化分离：

            • 各配置模块严格对应旧项目中的CGI程序
            • 保持配置文件格式100%兼容
            • 使用公共工具模块减少代码冗余
            • 遵循统一的项目代码结构

            请点击对应的配置模块按钮进入具体配置页面。
        `;

        if (typeof Utils !== 'undefined' && Utils.ui) {
            Utils.ui.showInfo('设备配置说明', infoMessage);
        } else {
            alert(infoMessage);
        }
    },

    /**
     * 处理导航到具体配置页面
     * 使用公共工具模块进行页面导航
     * @param {string} configType - 配置类型
     */
    navigateToConfig: function(configType) {
        const configPages = {
            'network-selection': '网络配置',
            'center-config': '呼叫中心配置',
            'gateway-config': '网关配置',
            'recorder-config': '录音配置',
            'sci-config': '基站配置',
            'switch-config': '交换机配置'
        };

        if (configPages[configType]) {
            if (typeof Router !== 'undefined') {
                Router.navigate(configType);
            } else {
                console.log(`导航到: ${configPages[configType]}`);
            }
        }
    }
};
