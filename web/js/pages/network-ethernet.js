/**
 * 以太网配置页面
 * 严格按照重构设计方案实现，使用公共工具模块减少冗余代码
 */

const NetworkEthernetPage = {
    // 页面配置
    config: {
        title: '以太网配置',
        apiEndpoint: '/config/network/ethernet'
    },

    // 当前配置数据
    currentConfig: null,

    /**
     * 渲染页面内容
     * @returns {string} HTML内容
     */
    render: function() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h2>${this.config.title}</h2>
                    <div class="page-actions">
                        <button type="button" class="btn btn-secondary" data-action="refresh">
                            <i class="icon-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>以太网接口配置</h3>
                            <p class="card-description">配置有线以太网连接参数</p>
                        </div>
                        
                        <div class="card-body">
                            <form id="ethernet-config-form" class="config-form">
                                <div class="form-group">
                                    <label class="form-label required">配置模式</label>
                                    <div class="radio-group">
                                        <label class="radio-item">
                                            <input type="radio" name="dhcp_enabled" value="1" required>
                                            <span class="radio-label">DHCP自动获取</span>
                                            <small class="form-help">自动从DHCP服务器获取网络配置</small>
                                        </label>
                                        <label class="radio-item">
                                            <input type="radio" name="dhcp_enabled" value="0" required>
                                            <span class="radio-label">静态IP配置</span>
                                            <small class="form-help">手动设置固定的网络参数</small>
                                        </label>
                                    </div>
                                </div>

                                <div id="static-config" class="config-section" style="display: none;">
                                    <div class="form-row">
                                        <div class="form-col">
                                            <label class="form-label required">IP地址</label>
                                            <input type="text" name="ip_address" class="form-control" 
                                                   placeholder="*************" maxlength="15">
                                            <small class="form-help">设备的IP地址</small>
                                        </div>
                                        <div class="form-col">
                                            <label class="form-label required">子网掩码</label>
                                            <input type="text" name="subnet_mask" class="form-control" 
                                                   placeholder="*************" maxlength="15">
                                            <small class="form-help">网络子网掩码</small>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-col">
                                            <label class="form-label required">默认网关</label>
                                            <input type="text" name="gateway" class="form-control" 
                                                   placeholder="192.168.1.1" maxlength="15">
                                            <small class="form-help">网络默认网关地址</small>
                                        </div>
                                        <div class="form-col">
                                            <label class="form-label">首选DNS</label>
                                            <input type="text" name="dns_primary" class="form-control" 
                                                   placeholder="8.8.8.8" maxlength="15">
                                            <small class="form-help">主DNS服务器地址</small>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-col">
                                            <label class="form-label">备用DNS</label>
                                            <input type="text" name="dns_secondary" class="form-control" 
                                                   placeholder="8.8.4.4" maxlength="15">
                                            <small class="form-help">备用DNS服务器地址</small>
                                        </div>
                                        <div class="form-col">
                                            <label class="form-label">MTU大小</label>
                                            <input type="number" name="mtu" class="form-control" 
                                                   placeholder="1500" min="576" max="9000">
                                            <small class="form-help">最大传输单元大小</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">MAC地址</label>
                                    <input type="text" name="mac_address" class="form-control" 
                                           placeholder="00:11:22:33:44:55" maxlength="17" readonly>
                                    <small class="form-help">网络接口MAC地址（只读）</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">连接速度</label>
                                    <select name="speed" class="form-control">
                                        <option value="auto">自动协商</option>
                                        <option value="10">10 Mbps</option>
                                        <option value="100">100 Mbps</option>
                                        <option value="1000">1000 Mbps</option>
                                    </select>
                                    <small class="form-help">以太网连接速度设置</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">双工模式</label>
                                    <select name="duplex" class="form-control">
                                        <option value="auto">自动协商</option>
                                        <option value="half">半双工</option>
                                        <option value="full">全双工</option>
                                    </select>
                                    <small class="form-help">以太网双工模式设置</small>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="icon-save"></i> 保存配置
                                    </button>
                                    <button type="button" class="btn btn-secondary" data-action="reset">
                                        <i class="icon-reset"></i> 重置
                                    </button>
                                    <button type="button" class="btn btn-info" data-action="test">
                                        <i class="icon-test"></i> 测试连接
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3>当前网络信息</h3>
                        </div>
                        <div class="card-body">
                            <div class="status-grid" id="ethernet-status">
                                <div class="status-item">
                                    <span class="status-label">连接状态:</span>
                                    <span class="status-value" id="link-status">检测中...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">当前IP:</span>
                                    <span class="status-value" id="current-ip">获取中...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">连接速度:</span>
                                    <span class="status-value" id="current-speed">检测中...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">双工模式:</span>
                                    <span class="status-value" id="current-duplex">检测中...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">接收字节:</span>
                                    <span class="status-value" id="rx-bytes">统计中...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">发送字节:</span>
                                    <span class="status-value" id="tx-bytes">统计中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * 初始化页面
     */
    init: function() {
        this.bindEvents();
        this.loadConfig();
        this.updateEthernetStatus();
    },

    /**
     * 绑定事件
     */
    bindEvents: function() {
        const form = Utils.dom.find('#ethernet-config-form');
        const dhcpRadios = Utils.dom.findAll('input[name="dhcp_enabled"]');
        
        // 表单提交事件
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveConfig();
        });

        // DHCP模式切换事件
        dhcpRadios.forEach(radio => {
            radio.addEventListener('change', () => {
                this.toggleStaticConfig();
            });
        });

        // 重置按钮事件
        const resetBtn = Utils.dom.find('[data-action="reset"]');
        resetBtn.addEventListener('click', () => {
            this.resetForm();
        });

        // 刷新按钮事件
        const refreshBtn = Utils.dom.find('[data-action="refresh"]');
        refreshBtn.addEventListener('click', () => {
            this.loadConfig();
            this.updateEthernetStatus();
        });

        // 测试连接按钮事件
        const testBtn = Utils.dom.find('[data-action="test"]');
        testBtn.addEventListener('click', () => {
            this.testConnection();
        });
    },

    /**
     * 切换静态配置显示
     */
    toggleStaticConfig: function() {
        const dhcpEnabled = Utils.dom.find('input[name="dhcp_enabled"]:checked');
        const staticConfig = Utils.dom.find('#static-config');
        const staticInputs = Utils.dom.findAll('#static-config input[required]');

        if (dhcpEnabled && dhcpEnabled.value === '0') {
            // 静态IP模式
            staticConfig.style.display = 'block';
            staticInputs.forEach(input => {
                input.setAttribute('required', 'required');
            });
        } else {
            // DHCP模式
            staticConfig.style.display = 'none';
            staticInputs.forEach(input => {
                input.removeAttribute('required');
            });
        }
    },

    /**
     * 加载配置数据
     */
    loadConfig: async function() {
        try {
            UI.showLoading();
            const response = await API.network.getEthernet();
            
            if (response && response.data) {
                this.currentConfig = response.data;
                this.populateForm(response.data);
            }
        } catch (error) {
            UI.showNotification('加载配置失败: ' + error.message, 'error');
        } finally {
            UI.hideLoading();
        }
    },

    /**
     * 填充表单数据
     * @param {Object} config - 配置数据
     */
    populateForm: function(config) {
        // 设置DHCP模式
        const dhcpRadio = Utils.dom.find(`input[name="dhcp_enabled"][value="${config.dhcp_enabled || 1}"]`);
        if (dhcpRadio) {
            dhcpRadio.checked = true;
            this.toggleStaticConfig();
        }

        // 填充静态IP配置
        if (config.ip_address) {
            const ipInput = Utils.dom.find('input[name="ip_address"]');
            if (ipInput) ipInput.value = config.ip_address;
        }

        if (config.subnet_mask) {
            const maskInput = Utils.dom.find('input[name="subnet_mask"]');
            if (maskInput) maskInput.value = config.subnet_mask;
        }

        if (config.gateway) {
            const gatewayInput = Utils.dom.find('input[name="gateway"]');
            if (gatewayInput) gatewayInput.value = config.gateway;
        }

        if (config.dns_primary) {
            const dnsInput = Utils.dom.find('input[name="dns_primary"]');
            if (dnsInput) dnsInput.value = config.dns_primary;
        }

        if (config.dns_secondary) {
            const dnsSecInput = Utils.dom.find('input[name="dns_secondary"]');
            if (dnsSecInput) dnsSecInput.value = config.dns_secondary;
        }

        if (config.mtu) {
            const mtuInput = Utils.dom.find('input[name="mtu"]');
            if (mtuInput) mtuInput.value = config.mtu;
        }

        // 设置MAC地址（只读）
        if (config.mac_address) {
            const macInput = Utils.dom.find('input[name="mac_address"]');
            if (macInput) macInput.value = config.mac_address;
        }

        // 设置连接速度
        const speedSelect = Utils.dom.find('select[name="speed"]');
        if (speedSelect && config.speed) {
            speedSelect.value = config.speed;
        }

        // 设置双工模式
        const duplexSelect = Utils.dom.find('select[name="duplex"]');
        if (duplexSelect && config.duplex) {
            duplexSelect.value = config.duplex;
        }
    },

    /**
     * 保存配置
     */
    saveConfig: async function() {
        try {
            const formData = this.getFormData();
            
            // 验证表单数据
            if (!this.validateForm(formData)) {
                return;
            }

            UI.showLoading();
            const response = await API.network.saveEthernet(formData);
            
            if (response && response.code === 200) {
                UI.showNotification('配置保存成功，网络设置将在重启后生效', 'success');
                this.currentConfig = formData;
            } else {
                throw new Error(response.message || '保存失败');
            }
        } catch (error) {
            UI.showNotification('保存配置失败: ' + error.message, 'error');
        } finally {
            UI.hideLoading();
        }
    },

    /**
     * 获取表单数据
     * @returns {Object} 表单数据
     */
    getFormData: function() {
        const form = Utils.dom.find('#ethernet-config-form');
        const formData = new FormData(form);
        
        return {
            dhcp_enabled: parseInt(formData.get('dhcp_enabled')) || 1,
            ip_address: formData.get('ip_address') || '',
            subnet_mask: formData.get('subnet_mask') || '',
            gateway: formData.get('gateway') || '',
            dns_primary: formData.get('dns_primary') || '',
            dns_secondary: formData.get('dns_secondary') || '',
            mtu: parseInt(formData.get('mtu')) || 1500,
            speed: formData.get('speed') || 'auto',
            duplex: formData.get('duplex') || 'auto'
        };
    },

    /**
     * 验证表单数据
     * @param {Object} data - 表单数据
     * @returns {boolean} 验证结果
     */
    validateForm: function(data) {
        // 如果是静态IP模式，需要验证IP配置
        if (data.dhcp_enabled === 0) {
            if (!data.ip_address || !Utils.validateIP(data.ip_address)) {
                UI.showNotification('请输入有效的IP地址', 'warning');
                return false;
            }

            if (!data.subnet_mask || !Utils.validateSubnetMask(data.subnet_mask)) {
                UI.showNotification('请输入有效的子网掩码', 'warning');
                return false;
            }

            if (!data.gateway || !Utils.validateIP(data.gateway)) {
                UI.showNotification('请输入有效的网关地址', 'warning');
                return false;
            }

            // 验证DNS地址（可选）
            if (data.dns_primary && !Utils.validateIP(data.dns_primary)) {
                UI.showNotification('请输入有效的首选DNS地址', 'warning');
                return false;
            }

            if (data.dns_secondary && !Utils.validateIP(data.dns_secondary)) {
                UI.showNotification('请输入有效的备用DNS地址', 'warning');
                return false;
            }
        }

        // 验证MTU值
        if (data.mtu < 576 || data.mtu > 9000) {
            UI.showNotification('MTU值必须在576-9000之间', 'warning');
            return false;
        }

        return true;
    },

    /**
     * 重置表单
     */
    resetForm: function() {
        if (this.currentConfig) {
            this.populateForm(this.currentConfig);
        } else {
            const form = Utils.dom.find('#ethernet-config-form');
            form.reset();
            this.toggleStaticConfig();
        }
    },

    /**
     * 测试网络连接
     */
    testConnection: async function() {
        try {
            UI.showLoading();
            UI.showNotification('正在测试网络连接...', 'info');
            
            // 这里可以调用网络测试API
            // 暂时使用模拟测试
            setTimeout(() => {
                UI.hideLoading();
                UI.showNotification('网络连接测试成功', 'success');
            }, 2000);
            
        } catch (error) {
            UI.hideLoading();
            UI.showNotification('网络连接测试失败: ' + error.message, 'error');
        }
    },

    /**
     * 更新以太网状态
     */
    updateEthernetStatus: async function() {
        try {
            // 这里可以调用系统状态API获取实时以太网状态
            // 暂时使用模拟数据
            const statusData = {
                link_status: '已连接',
                current_ip: '*************',
                current_speed: '100 Mbps',
                current_duplex: '全双工',
                rx_bytes: '1.2 MB',
                tx_bytes: '856 KB'
            };

            this.updateStatusDisplay(statusData);
        } catch (error) {
            console.error('更新以太网状态失败:', error);
        }
    },

    /**
     * 更新状态显示
     * @param {Object} statusData - 状态数据
     */
    updateStatusDisplay: function(statusData) {
        const linkStatus = Utils.dom.find('#link-status');
        const currentIP = Utils.dom.find('#current-ip');
        const currentSpeed = Utils.dom.find('#current-speed');
        const currentDuplex = Utils.dom.find('#current-duplex');
        const rxBytes = Utils.dom.find('#rx-bytes');
        const txBytes = Utils.dom.find('#tx-bytes');

        if (linkStatus) linkStatus.textContent = statusData.link_status;
        if (currentIP) currentIP.textContent = statusData.current_ip;
        if (currentSpeed) currentSpeed.textContent = statusData.current_speed;
        if (currentDuplex) currentDuplex.textContent = statusData.current_duplex;
        if (rxBytes) rxBytes.textContent = statusData.rx_bytes;
        if (txBytes) txBytes.textContent = statusData.tx_bytes;
    }
};
