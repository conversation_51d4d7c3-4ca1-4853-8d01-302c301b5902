/**
 * 主应用模块
 * 应用程序入口和全局管理
 * 按照重构设计方案实现现代化前端应用架构
 */

const App = {
    // 应用配置
    config: {
        version: '2.0.0',
        apiTimeout: 30000,
        autoSaveInterval: 30000,
        theme: 'default'
    },

    // 应用状态
    state: {
        initialized: false,
        connected: true,
        currentUser: 'admin',
        lastActivity: Date.now()
    },

    /**
     * 应用初始化
     */
    init: async function() {
        try {
            console.log('正在初始化VICTEL IP交换机配置系统...');
            
            // 检查浏览器兼容性
            this.checkBrowserCompatibility();
            
            // 加载用户配置
            this.loadUserConfig();
            
            // 初始化各个模块
            await this.initializeModules();
            
            // 绑定全局事件
            this.bindGlobalEvents();
            
            // 启动应用
            this.startApplication();
            
            // 标记为已初始化
            this.state.initialized = true;
            
            console.log('应用初始化完成');
            
        } catch (error) {
            console.error('应用初始化失败:', error);
            this.handleInitializationError(error);
        }
    },

    /**
     * 检查浏览器兼容性
     */
    checkBrowserCompatibility: function() {
        const requiredFeatures = [
            'fetch',
            'Promise',
            'localStorage',
            'addEventListener'
        ];

        const missingFeatures = requiredFeatures.filter(feature => {
            return !(feature in window);
        });

        if (missingFeatures.length > 0) {
            throw new Error(`浏览器不支持以下功能: ${missingFeatures.join(', ')}`);
        }

        // 检查CSS Grid支持
        if (!CSS.supports('display', 'grid')) {
            console.warn('浏览器不支持CSS Grid，可能影响布局效果');
        }
    },

    /**
     * 加载用户配置
     */
    loadUserConfig: function() {
        const savedConfig = Utils.storage.get('app_config', {});
        this.config = { ...this.config, ...savedConfig };
        
        const savedState = Utils.storage.get('app_state', {});
        this.state = { ...this.state, ...savedState };
    },

    /**
     * 保存用户配置
     */
    saveUserConfig: function() {
        Utils.storage.set('app_config', this.config);
        Utils.storage.set('app_state', {
            currentUser: this.state.currentUser,
            lastActivity: this.state.lastActivity
        });
    },

    /**
     * 初始化各个模块
     */
    initializeModules: async function() {
        // 初始化UI管理器
        if (window.UI) {
            UI.init();
        }

        // 初始化路由器
        if (window.Router) {
            Router.init();
        }

        // 检查API连接
        await this.checkAPIConnection();
    },

    /**
     * 检查API连接
     */
    checkAPIConnection: async function() {
        try {
            // 尝试获取网络配置来测试连接
            await API.network.getSelection();
            this.state.connected = true;
            if (window.UI) {
                UI.setConnectionStatus(true);
            }
        } catch (error) {
            console.warn('API连接检查失败:', error);
            this.state.connected = false;
            if (window.UI) {
                UI.setConnectionStatus(false);
                UI.showNotification('与服务器连接失败，请检查网络连接', 'warning');
            }
        }
    },

    /**
     * 绑定全局事件
     */
    bindGlobalEvents: function() {
        // 监听窗口关闭事件
        window.addEventListener('beforeunload', (e) => {
            this.saveUserConfig();
        });

        // 监听在线/离线状态
        window.addEventListener('online', () => {
            this.state.connected = true;
            if (window.UI) {
                UI.setConnectionStatus(true);
                UI.showNotification('网络连接已恢复', 'success');
            }
        });

        window.addEventListener('offline', () => {
            this.state.connected = false;
            if (window.UI) {
                UI.setConnectionStatus(false);
                UI.showNotification('网络连接已断开', 'warning');
            }
        });

        // 监听用户活动
        ['click', 'keydown', 'mousemove', 'scroll'].forEach(event => {
            document.addEventListener(event, Utils.throttle(() => {
                this.updateLastActivity();
            }, 1000));
        });

        // 监听路由变化
        window.addEventListener('routechange', (e) => {
            this.onRouteChange(e.detail);
        });

        // 监听错误事件
        window.addEventListener('error', (e) => {
            this.handleGlobalError(e.error);
        });

        // 监听未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (e) => {
            this.handleGlobalError(e.reason);
        });

        // 导航链接点击事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('.nav-link[data-page]')) {
                e.preventDefault();
                const page = e.target.getAttribute('data-page');
                if (window.Router) {
                    Router.navigateTo(page);
                }
                // 移动端点击导航后关闭菜单
                this.closeMobileMenu();
            }
        });

        // 移动端菜单切换事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('.mobile-menu-toggle') || e.target.closest('.mobile-menu-toggle')) {
                e.preventDefault();
                this.toggleMobileMenu();
            }
        });

        // 点击侧边栏外部关闭菜单
        document.addEventListener('click', (e) => {
            const sidebar = document.querySelector('.app-sidebar');
            const menuToggle = document.querySelector('.mobile-menu-toggle');

            if (sidebar && sidebar.classList.contains('active') &&
                !sidebar.contains(e.target) &&
                menuToggle && !menuToggle.contains(e.target)) {
                this.closeMobileMenu();
            }
        });

        // 窗口大小改变事件
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // ESC键关闭移动菜单
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeMobileMenu();
            }
        });

        // 表单提交事件
        document.addEventListener('submit', (e) => {
            if (e.target.matches('.config-form')) {
                e.preventDefault();
                this.handleFormSubmit(e.target);
            }
        });

        // 通知关闭事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('#notification-close')) {
                if (window.UI) {
                    UI.hideNotification();
                }
            }
        });
    },

    /**
     * 启动应用
     */
    startApplication: function() {
        // 显示应用界面
        document.body.style.visibility = 'visible';
        
        // 启动定期任务
        this.startPeriodicTasks();
        
        // 触发应用启动事件
        const event = new CustomEvent('appstart', {
            detail: { config: this.config, state: this.state }
        });
        window.dispatchEvent(event);
    },

    /**
     * 启动定期任务
     */
    startPeriodicTasks: function() {
        // 定期检查连接状态
        setInterval(() => {
            this.checkAPIConnection();
        }, 30000);

        // 定期保存配置
        setInterval(() => {
            this.saveUserConfig();
        }, this.config.autoSaveInterval);

        // 定期清理过期数据
        setInterval(() => {
            this.cleanupExpiredData();
        }, 300000); // 5分钟
    },

    /**
     * 更新最后活动时间
     */
    updateLastActivity: function() {
        this.state.lastActivity = Date.now();
    },

    /**
     * 路由变化处理
     * @param {Object} detail - 路由详情
     */
    onRouteChange: function(detail) {
        console.log('路由变化:', detail.route);
        this.updateLastActivity();
    },

    /**
     * 处理全局错误
     * @param {Error} error - 错误对象
     */
    handleGlobalError: function(error) {
        console.error('全局错误:', error);
        
        if (window.UI) {
            UI.showNotification('系统发生错误: ' + error.message, 'error');
        }
        
        // 记录错误到本地存储
        const errorLog = Utils.storage.get('error_log', []);
        errorLog.push({
            timestamp: Date.now(),
            message: error.message,
            stack: error.stack,
            url: window.location.href
        });
        
        // 限制错误日志数量
        if (errorLog.length > 100) {
            errorLog.splice(0, 50);
        }
        
        Utils.storage.set('error_log', errorLog);
    },

    /**
     * 处理初始化错误
     * @param {Error} error - 错误对象
     */
    handleInitializationError: function(error) {
        document.body.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; height: 100vh; flex-direction: column; font-family: Arial, sans-serif;">
                <h1 style="color: #dc3545;">应用初始化失败</h1>
                <p style="color: #6c757d; margin-bottom: 2rem;">${error.message}</p>
                <button onclick="location.reload()" style="padding: 0.5rem 1rem; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">重新加载</button>
            </div>
        `;
    },

    /**
     * 清理过期数据
     */
    cleanupExpiredData: function() {
        // 清理过期的错误日志
        const errorLog = Utils.storage.get('error_log', []);
        const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
        const filteredLog = errorLog.filter(log => log.timestamp > oneWeekAgo);
        Utils.storage.set('error_log', filteredLog);
    },

    /**
     * 切换移动端菜单
     */
    toggleMobileMenu: function() {
        const sidebar = document.querySelector('.app-sidebar');
        if (sidebar) {
            sidebar.classList.toggle('active');
        }
    },

    /**
     * 关闭移动端菜单
     */
    closeMobileMenu: function() {
        const sidebar = document.querySelector('.app-sidebar');
        if (sidebar) {
            sidebar.classList.remove('active');
        }
    },

    /**
     * 处理窗口大小改变
     */
    handleResize: function() {
        // 在大屏幕上自动关闭移动菜单
        if (window.innerWidth > 768) {
            this.closeMobileMenu();
        }
    },

    /**
     * 处理表单提交
     * @param {HTMLFormElement} form - 表单元素
     */
    handleFormSubmit: function(form) {
        const formData = new FormData(form);
        const data = {};

        // 转换FormData为普通对象
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }

        // 获取表单的action属性或data-action属性
        const action = form.getAttribute('action') || form.getAttribute('data-action');

        if (action && window.API) {
            // 显示加载状态
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn ? submitBtn.textContent : '';
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.textContent = '保存中...';
            }

            // 发送API请求
            API.post(action, data)
                .then(response => {
                    if (window.UI) {
                        UI.showNotification('配置保存成功', 'success');
                    }
                })
                .catch(error => {
                    console.error('表单提交失败:', error);
                    if (window.UI) {
                        UI.showNotification('配置保存失败: ' + error.message, 'error');
                    }
                })
                .finally(() => {
                    // 恢复按钮状态
                    if (submitBtn) {
                        submitBtn.disabled = false;
                        submitBtn.textContent = originalText;
                    }
                });
        }
    },

    /**
     * 获取应用信息
     * @returns {Object} 应用信息
     */
    getAppInfo: function() {
        return {
            version: this.config.version,
            initialized: this.state.initialized,
            connected: this.state.connected,
            currentUser: this.state.currentUser,
            lastActivity: new Date(this.state.lastActivity).toLocaleString()
        };
    }
};

// 当DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    App.init();
});

// 导出到全局作用域
window.App = App;
