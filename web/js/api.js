/**
 * API通信模块
 * 基于fetch API实现统一的后端通信接口
 */

const API = {
    // API基础配置
    baseURL: '/api/v1',
    timeout: 30000,
    
    /**
     * 统一的HTTP请求方法
     * @param {string} method - HTTP方法
     * @param {string} endpoint - API端点
     * @param {Object} data - 请求数据
     * @param {Object} options - 额外选项
     * @returns {Promise} 请求结果
     */
    request: async function(method, endpoint, data = null, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        
        const config = {
            method: method.toUpperCase(),
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                ...options.headers
            },
            ...options
        };
        
        // 添加请求体
        if (data && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
            config.body = JSON.stringify(data);
        }
        
        // 设置超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.timeout);
        config.signal = controller.signal;
        
        try {
            const response = await fetch(url, config);
            clearTimeout(timeoutId);
            
            // 检查响应状态
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            // 解析JSON响应
            const result = await response.json();
            
            // 检查业务状态码
            if (result.code && result.code !== 200) {
                throw new Error(result.message || '请求失败');
            }
            
            return result;
            
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('请求超时');
            }
            
            throw error;
        }
    },

    /**
     * GET请求
     * @param {string} endpoint - API端点
     * @param {Object} options - 额外选项
     * @returns {Promise} 请求结果
     */
    get: function(endpoint, options = {}) {
        return this.request('GET', endpoint, null, options);
    },

    /**
     * POST请求
     * @param {string} endpoint - API端点
     * @param {Object} data - 请求数据
     * @param {Object} options - 额外选项
     * @returns {Promise} 请求结果
     */
    post: function(endpoint, data, options = {}) {
        return this.request('POST', endpoint, data, options);
    },

    /**
     * PUT请求
     * @param {string} endpoint - API端点
     * @param {Object} data - 请求数据
     * @param {Object} options - 额外选项
     * @returns {Promise} 请求结果
     */
    put: function(endpoint, data, options = {}) {
        return this.request('PUT', endpoint, data, options);
    },

    /**
     * DELETE请求
     * @param {string} endpoint - API端点
     * @param {Object} options - 额外选项
     * @returns {Promise} 请求结果
     */
    delete: function(endpoint, options = {}) {
        return this.request('DELETE', endpoint, null, options);
    },

    // 网络配置API
    network: {
        /**
         * 获取网络选择配置
         */
        getSelection: function() {
            return API.get('/config/network/selection');
        },

        /**
         * 保存网络选择配置
         * @param {Object} config - 配置数据
         */
        saveSelection: function(config) {
            return API.post('/config/network/selection', config);
        },

        /**
         * 获取以太网配置
         */
        getEthernet: function() {
            return API.get('/config/network/ethernet');
        },

        /**
         * 保存以太网配置
         * @param {Object} config - 配置数据
         */
        saveEthernet: function(config) {
            return API.post('/config/network/ethernet', config);
        }
    },

    // 配置管理API（严格对应旧项目CGI程序）
    config: {
        /**
         * 获取呼叫中心配置（对应旧项目配置文件）
         */
        getCenter: function() {
            return API.get('/config/center');
        },

        /**
         * 保存呼叫中心配置
         * @param {Object} config - 配置数据
         */
        saveCenter: function(config) {
            return API.post('/config/center', config);
        },

        /**
         * 获取网关配置
         */
        getGateway: function() {
            return API.get('/config/gateway');
        },

        /**
         * 保存网关配置
         * @param {Object} config - 配置数据
         */
        saveGateway: function(config) {
            return API.post('/config/gateway', config);
        },

        /**
         * 获取录音配置
         */
        getRecorder: function() {
            return API.get('/config/recorder');
        },

        /**
         * 保存录音配置
         * @param {Object} config - 配置数据
         */
        saveRecorder: function(config) {
            return API.post('/config/recorder', config);
        },

        /**
         * 获取SCI基站配置
         */
        getSCI: function() {
            return API.get('/config/sci');
        },

        /**
         * 保存SCI基站配置
         * @param {Object} config - 配置数据
         */
        saveSCI: function(config) {
            return API.post('/config/sci', config);
        },

        /**
         * 获取交换机配置
         */
        getSwitch: function() {
            return API.get('/config/switch');
        },

        /**
         * 保存交换机配置
         * @param {Object} config - 配置数据
         */
        saveSwitch: function(config) {
            return API.post('/config/switch', config);
        }
    },

    // 系统管理API（仅保留对应0system.c和0down.c的功能）
    system: {
        /**
         * 获取系统日志（对应0down.c的日志显示功能）
         */
        getLogs: function() {
            return API.get('/system/logs');
        },

        /**
         * 获取信号强度（对应0down.c的3G信号检测功能）
         */
        getSignal: function() {
            return API.get('/system/signal');
        },

        /**
         * 系统重启（对应0system.c的reboot功能）
         */
        restart: function() {
            return API.post('/system/restart');
        },

        /**
         * 配置重置（对应0system.c的reset功能）
         */
        reset: function() {
            return API.post('/system/reset');
        }
    },

    // NTP配置API
    ntp: {
        /**
         * 获取NTP配置
         */
        get: function() {
            return API.get('/config/ntp');
        },

        /**
         * 保存NTP配置
         * @param {Object} config - 配置数据
         */
        save: function(config) {
            return API.post('/config/ntp', config);
        }
    },

    // 认证API
    auth: {
        /**
         * 修改密码
         * @param {Object} data - 密码数据
         */
        changePassword: function(data) {
            return API.post('/auth/password', data);
        }
    },

    /**
     * 批量请求处理
     * @param {Array} requests - 请求数组
     * @returns {Promise} 所有请求的结果
     */
    batch: async function(requests) {
        try {
            const promises = requests.map(req => {
                const { method, endpoint, data, options } = req;
                return this.request(method, endpoint, data, options);
            });
            
            return await Promise.allSettled(promises);
        } catch (error) {
            throw error;
        }
    },

    /**
     * 上传文件
     * @param {string} endpoint - API端点
     * @param {File} file - 文件对象
     * @param {Function} onProgress - 进度回调
     * @returns {Promise} 上传结果
     */
    upload: async function(endpoint, file, onProgress = null) {
        const url = `${this.baseURL}${endpoint}`;
        const formData = new FormData();
        formData.append('file', file);
        
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            // 监听上传进度
            if (onProgress) {
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        onProgress(percentComplete);
                    }
                });
            }
            
            // 监听响应
            xhr.addEventListener('load', () => {
                if (xhr.status >= 200 && xhr.status < 300) {
                    try {
                        const result = JSON.parse(xhr.responseText);
                        resolve(result);
                    } catch (e) {
                        resolve(xhr.responseText);
                    }
                } else {
                    reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
                }
            });
            
            // 监听错误
            xhr.addEventListener('error', () => {
                reject(new Error('网络错误'));
            });
            
            // 监听超时
            xhr.addEventListener('timeout', () => {
                reject(new Error('请求超时'));
            });
            
            // 设置超时时间
            xhr.timeout = this.timeout;
            
            // 发送请求
            xhr.open('POST', url);
            xhr.send(formData);
        });
    }
};
