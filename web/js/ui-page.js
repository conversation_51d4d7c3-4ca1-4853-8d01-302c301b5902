/**
 * UI页面管理模块
 * 负责页面内容的加载、渲染和事件绑定
 * 从ui.js拆分出来，专门处理页面相关的逻辑
 */

const UIPage = {
    /**
     * 初始化页面管理器
     */
    init: function() {
        console.log('页面管理器初始化完成');
    },

    /**
     * 加载页面内容
     * @param {string} page - 页面名称
     */
    loadPageContent: function(page) {
        console.log('开始加载页面内容:', page);
        
        try {
            this.renderPageContent(page);
            UICore.hideLoading();
            console.log('页面内容加载完成:', page);
        } catch (error) {
            console.error('页面加载失败:', error);
            this.renderErrorPage(error);
            UICore.hideLoading();
        }
    },

    /**
     * 渲染页面内容
     * @param {string} page - 页面名称
     */
    renderPageContent: function(page) {
        let content = '';
        
        switch (page) {
            case 'network-selection':
                content = this.renderNetworkSelectionPage();
                break;
            case 'network-ethernet':
                content = this.renderNetworkEthernetPage();
                break;
            case 'device-config':
                content = this.renderDeviceConfigPage();
                break;
            case 'center':
                content = this.renderCenterConfigPage();
                break;
            case 'gateway':
                content = this.renderGatewayConfigPage();
                break;
            case 'recorder':
                content = this.renderRecorderConfigPage();
                break;
            case 'sci':
                content = this.renderSCIConfigPage();
                break;
            case 'switch':
                content = this.renderSwitchConfigPage();
                break;
            case 'system-logs':
                content = this.renderSystemLogsPage();
                break;
            case 'system-signal':
                content = this.renderSystemSignalPage();
                break;
            case 'system-reboot':
                content = this.renderSystemRebootPage();
                break;
            case 'system-reset':
                content = this.renderSystemResetPage();
                break;
            case 'ntp':
                content = this.renderNTPConfigPage();
                break;
            case 'auth':
                content = this.renderAuthPage();
                break;
            case 'system':
                content = this.renderSystemPage();
                break;
            default:
                content = this.renderDefaultPage();
        }
        
        const mainContent = UICore.getElement('mainContent');
        if (mainContent) {
            mainContent.innerHTML = content;
            this.bindPageEvents(page);
        }
    },

    /**
     * 渲染错误页面
     * @param {Error} error - 错误对象
     */
    renderErrorPage: function(error) {
        const content = `
            <div class="card">
                <div class="card-body text-center">
                    <h3>页面加载失败</h3>
                    <p class="text-muted">${error.message}</p>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                </div>
            </div>
        `;
        
        const mainContent = UICore.getElement('mainContent');
        if (mainContent) {
            mainContent.innerHTML = content;
        }
    },

    /**
     * 绑定页面特定事件
     * @param {string} page - 页面名称
     */
    bindPageEvents: function(page) {
        const mainContent = UICore.getElement('mainContent');
        if (!mainContent) return;

        // 绑定表单提交事件
        const forms = Utils.dom.findAll('form', mainContent);
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmit(form, page);
            });
        });
        
        // 绑定按钮点击事件
        const buttons = Utils.dom.findAll('button', mainContent);
        buttons.forEach(button => {
            const action = button.dataset.action;
            if (action) {
                button.addEventListener('click', () => {
                    this.handleButtonAction(action, page);
                });
            }
        });
    },

    /**
     * 处理表单提交
     * @param {Element} form - 表单元素
     * @param {string} page - 页面名称
     */
    handleFormSubmit: function(form, page) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        UICore.showLoading();
        
        // 根据页面类型调用相应的API
        this.savePageConfig(page, data)
            .then(() => {
                if (window.UINotification) {
                    UINotification.success('配置保存成功');
                }
            })
            .catch(error => {
                if (window.UINotification) {
                    UINotification.error('配置保存失败: ' + error.message);
                }
            })
            .finally(() => {
                UICore.hideLoading();
            });
    },

    /**
     * 处理按钮操作
     * @param {string} action - 操作类型
     * @param {string} page - 页面名称
     */
    handleButtonAction: function(action, page) {
        switch (action) {
            case 'reboot':
                if (window.UIModal) UIModal.confirmReboot();
                break;
            case 'reset':
                if (window.UIModal) UIModal.confirmReset();
                break;
            case 'refresh':
                this.refreshPageData(page);
                break;
            default:
                console.log('Unknown action:', action);
        }
    },

    /**
     * 保存页面配置
     * @param {string} page - 页面名称
     * @param {Object} data - 配置数据
     * @returns {Promise} 保存结果
     */
    savePageConfig: async function(page, data) {
        const apiMap = {
            'network-selection': () => API.network.saveSelection(data),
            'network-ethernet': () => API.network.saveEthernet(data),
            'center': () => API.device.saveCenter(data),
            'gateway': () => API.device.saveGateway(data),
            'recorder': () => API.device.saveRecorder(data),
            'sci': () => API.device.saveSCI(data),
            'switch': () => API.device.saveSwitch(data),
            'ntp': () => API.ntp.save(data),
            'auth': () => API.auth.changePassword(data)
        };
        
        const apiCall = apiMap[page];
        if (apiCall) {
            return await apiCall();
        } else {
            throw new Error('未知的页面类型');
        }
    },

    /**
     * 刷新页面数据
     * @param {string} page - 页面名称
     */
    refreshPageData: function(page) {
        UICore.showLoading();
        this.loadPageContent(page);
    }
};

// 页面渲染方法将在后续任务中补全
UIPage.renderNetworkSelectionPage = function() {
    if (window.NetworkSelectionPage && NetworkSelectionPage.render) {
        const content = NetworkSelectionPage.render();
        setTimeout(() => {
            if (NetworkSelectionPage.init) NetworkSelectionPage.init();
        }, 100);
        return content;
    }
    return '<div class="card"><div class="card-header">网络选择配置</div><div class="card-body">网络选择配置页面内容...</div></div>';
};

// 其他页面渲染方法的占位符
UIPage.renderNetworkEthernetPage = function() {
    if (window.NetworkEthernetPage && NetworkEthernetPage.render) {
        const content = NetworkEthernetPage.render();
        setTimeout(() => {
            if (NetworkEthernetPage.init) NetworkEthernetPage.init();
        }, 100);
        return content;
    }
    return '<div class="card"><div class="card-header">以太网配置</div><div class="card-body">以太网配置页面内容...</div></div>';
};

UIPage.renderDeviceConfigPage = function() {
    if (window.DeviceConfigPage && DeviceConfigPage.render) {
        const content = DeviceConfigPage.render();
        setTimeout(() => {
            if (DeviceConfigPage.init) DeviceConfigPage.init();
        }, 100);
        return content;
    }
    return '<div class="card"><div class="card-header">设备配置</div><div class="card-body">设备配置页面内容...</div></div>';
};

UIPage.renderSystemPage = function() {
    if (window.SystemManagementPage && SystemManagementPage.render) {
        const content = SystemManagementPage.render();
        setTimeout(() => {
            if (SystemManagementPage.init) SystemManagementPage.init();
        }, 100);
        return content;
    }
    return '<div class="card"><div class="card-header">系统管理</div><div class="card-body">系统管理页面内容...</div></div>';
};

// 简单页面渲染方法
UIPage.renderCenterConfigPage = function() {
    return '<div class="card"><div class="card-header">呼叫中心配置</div><div class="card-body">呼叫中心配置页面内容...</div></div>';
};

UIPage.renderGatewayConfigPage = function() {
    return '<div class="card"><div class="card-header">互联网关配置</div><div class="card-body">互联网关配置页面内容...</div></div>';
};

UIPage.renderRecorderConfigPage = function() {
    return '<div class="card"><div class="card-header">录音配置</div><div class="card-body">录音配置页面内容...</div></div>';
};

UIPage.renderSCIConfigPage = function() {
    return '<div class="card"><div class="card-header">SCI基站配置</div><div class="card-body">SCI基站配置页面内容...</div></div>';
};

UIPage.renderSwitchConfigPage = function() {
    return '<div class="card"><div class="card-header">交换机配置</div><div class="card-body">交换机配置页面内容...</div></div>';
};

UIPage.renderSystemLogsPage = function() {
    if (window.SystemManagementPage && SystemManagementPage.renderLogs) {
        const content = SystemManagementPage.renderLogs();
        setTimeout(() => {
            if (SystemManagementPage.initLogs) SystemManagementPage.initLogs();
        }, 100);
        return content;
    }
    return '<div class="card"><div class="card-header">系统日志</div><div class="card-body">系统日志页面内容...</div></div>';
};

UIPage.renderSystemSignalPage = function() {
    if (window.SystemManagementPage && SystemManagementPage.renderSignal) {
        const content = SystemManagementPage.renderSignal();
        setTimeout(() => {
            if (SystemManagementPage.initSignal) SystemManagementPage.initSignal();
        }, 100);
        return content;
    }
    return '<div class="card"><div class="card-header">信号检测</div><div class="card-body">信号检测页面内容...</div></div>';
};

UIPage.renderSystemRebootPage = function() {
    if (window.SystemManagementPage && SystemManagementPage.renderReboot) {
        const content = SystemManagementPage.renderReboot();
        setTimeout(() => {
            if (SystemManagementPage.initReboot) SystemManagementPage.initReboot();
        }, 100);
        return content;
    }
    return '<div class="card"><div class="card-header">系统重启</div><div class="card-body">系统重启页面内容...</div></div>';
};

UIPage.renderSystemResetPage = function() {
    if (window.SystemManagementPage && SystemManagementPage.renderReset) {
        const content = SystemManagementPage.renderReset();
        setTimeout(() => {
            if (SystemManagementPage.initReset) SystemManagementPage.initReset();
        }, 100);
        return content;
    }
    return '<div class="card"><div class="card-header">配置重置</div><div class="card-body">配置重置页面内容...</div></div>';
};

UIPage.renderNTPConfigPage = function() {
    return '<div class="card"><div class="card-header">NTP时间同步</div><div class="card-body">NTP时间同步配置页面内容...</div></div>';
};

UIPage.renderAuthPage = function() {
    return '<div class="card"><div class="card-header">密码修改</div><div class="card-body">密码修改页面内容...</div></div>';
};

UIPage.renderDefaultPage = function() {
    return '<div class="card"><div class="card-header">欢迎</div><div class="card-body">欢迎使用VICTEL IP交换机配置系统</div></div>';
};

// 导出到全局作用域，保持向后兼容
window.UIPage = UIPage;
