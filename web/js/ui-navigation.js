/**
 * UI导航管理模块
 * 负责导航菜单、页面切换、面包屑更新等功能
 * 从ui.js拆分出来，专门处理导航相关的逻辑
 */

const UINavigation = {
    /**
     * 初始化导航管理器
     */
    init: function() {
        this.bindNavigationEvents();
        this.loadDefaultPage();
    },

    /**
     * 绑定导航相关事件
     */
    bindNavigationEvents: function() {
        const elements = UICore.getAllElements();
        
        // 导航菜单点击事件
        if (elements.navLinks) {
            elements.navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const page = link.dataset.page;
                    if (page) {
                        this.navigateToPage(page);
                        this.setActiveNavLink(link);
                    }
                });
            });
        }

        // 退出登录事件
        if (elements.logoutBtn) {
            elements.logoutBtn.addEventListener('click', () => {
                this.confirmLogout();
            });
        }

        // 键盘事件（ESC键）
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                if (window.UIModal) UIModal.hide();
                if (window.UINotification) UINotification.hide();
            }
        });
    },

    /**
     * 加载默认页面
     */
    loadDefaultPage: function() {
        console.log('开始加载默认页面');
        this.navigateToPage(UICore.currentPage);
    },

    /**
     * 导航到指定页面
     * @param {string} page - 页面名称
     */
    navigateToPage: function(page) {
        UICore.currentPage = page;
        UICore.showLoading();

        // 更新面包屑
        this.updateBreadcrumb(page);

        // 加载页面内容
        if (window.UIPage) {
            UIPage.loadPageContent(page);
        }

        // 在移动端关闭侧边栏
        if (window.innerWidth <= 768) {
            const sidebar = UICore.getElement('sidebar');
            if (sidebar) {
                Utils.dom.removeClass(sidebar, 'active');
            }
        }
    },

    /**
     * 设置活动导航链接
     * @param {Element} activeLink - 活动链接元素
     */
    setActiveNavLink: function(activeLink) {
        const navLinks = UICore.getElement('navLinks');
        if (!navLinks) return;

        // 移除所有活动状态
        navLinks.forEach(link => {
            Utils.dom.removeClass(link, 'active');
        });
        
        // 设置当前活动状态
        Utils.dom.addClass(activeLink, 'active');
        
        // 展开父级菜单
        const parentItem = activeLink.closest('.nav-item');
        if (parentItem) {
            Utils.dom.addClass(parentItem, 'active');
        }
    },

    /**
     * 更新面包屑导航
     * @param {string} page - 页面名称
     */
    updateBreadcrumb: function(page) {
        const breadcrumbMap = {
            'network': '网络配置',
            'network-selection': '网络配置 > 网络选择',
            'network-ethernet': '网络配置 > 以太网配置',
            'device': '设备配置',
            'center': '设备配置 > 呼叫中心',
            'gateway': '设备配置 > 互联网关',
            'recorder': '设备配置 > 录音配置',
            'sci': '设备配置 > 基站配置',
            'switch': '设备配置 > 交换配置',
            'system': '系统管理',
            'system-logs': '系统管理 > 系统日志',
            'system-signal': '系统管理 > 信号检测',
            'system-reboot': '系统管理 > 系统重启',
            'system-reset': '系统管理 > 配置重置',
            'ntp': '时间同步',
            'auth': '密码修改'
        };
        
        const breadcrumb = UICore.getElement('breadcrumb');
        if (breadcrumb) {
            breadcrumb.textContent = breadcrumbMap[page] || page;
        }
    },

    /**
     * 确认退出登录
     */
    confirmLogout: function() {
        if (window.UIModal) {
            UIModal.show(
                '确认退出',
                '您确定要退出系统吗？',
                () => {
                    window.location.href = '/login.html';
                }
            );
        }
    },

    /**
     * 刷新当前页面
     */
    refreshCurrentPage: function() {
        this.navigateToPage(UICore.currentPage);
    },

    /**
     * 获取当前页面名称
     * @returns {string} 当前页面名称
     */
    getCurrentPage: function() {
        return UICore.currentPage;
    }
};

// 导出到全局作用域，保持向后兼容
window.UINavigation = UINavigation;
