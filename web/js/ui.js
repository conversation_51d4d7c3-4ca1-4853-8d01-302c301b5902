/**
 * UI管理模块
 * 负责用户界面的交互和状态管理
 * 按照重构设计方案实现现代化前端界面
 */

const UI = {
    // UI元素缓存
    elements: {},

    // 当前页面状态
    currentPage: 'network-selection',

    /**
     * 初始化UI管理器
     */
    init: function() {
        this.cacheElements();
        this.bindEvents();
        this.initializeComponents();
        this.initMobileMenu();
    },

    /**
     * 缓存常用DOM元素
     */
    cacheElements: function() {
        this.elements = {
            // 主要容器
            mainContent: Utils.dom.find('#main-content'),
            loading: Utils.dom.find('#loading'),
            breadcrumb: Utils.dom.find('#breadcrumb-text'),
            statusText: Utils.dom.find('#status-text'),
            connectionStatus: Utils.dom.find('#connection-status'),

            // 导航菜单
            navMenu: Utils.dom.find('#nav-menu'),
            navLinks: Utils.dom.findAll('.nav-link'),
            sidebar: Utils.dom.find('.app-sidebar'),

            // 模态框
            modal: Utils.dom.find('#modal'),
            modalTitle: Utils.dom.find('#modal-title'),
            modalBody: Utils.dom.find('#modal-body'),
            modalClose: Utils.dom.find('#modal-close'),
            modalCancel: Utils.dom.find('#modal-cancel'),
            modalConfirm: Utils.dom.find('#modal-confirm'),

            // 通知
            notification: Utils.dom.find('#notification'),
            notificationText: Utils.dom.find('#notification-text'),
            notificationClose: Utils.dom.find('#notification-close'),

            // 其他
            logoutBtn: Utils.dom.find('#logout-btn')
        };
    },

    /**
     * 绑定事件监听器
     */
    bindEvents: function() {
        // 导航菜单点击事件
        this.elements.navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.dataset.page;
                if (page) {
                    this.navigateToPage(page);
                    this.setActiveNavLink(link);
                }
            });
        });

        // 模态框事件
        if (this.elements.modalClose) {
            this.elements.modalClose.addEventListener('click', () => {
                this.hideModal();
            });
        }

        if (this.elements.modalCancel) {
            this.elements.modalCancel.addEventListener('click', () => {
                this.hideModal();
            });
        }

        if (this.elements.modal) {
            this.elements.modal.addEventListener('click', (e) => {
                if (e.target === this.elements.modal) {
                    this.hideModal();
                }
            });
        }

        // 通知关闭事件
        if (this.elements.notificationClose) {
            this.elements.notificationClose.addEventListener('click', () => {
                this.hideNotification();
            });
        }

        // 退出登录事件
        if (this.elements.logoutBtn) {
            this.elements.logoutBtn.addEventListener('click', () => {
                this.confirmLogout();
            });
        }

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideModal();
                this.hideNotification();
            }
        });
    },

    /**
     * 初始化组件
     */
    initializeComponents: function() {
        // 设置初始状态
        this.setStatus('就绪');
        this.setConnectionStatus(true);

        // 检查DOM元素是否正确缓存
        if (!this.elements.mainContent) {
            console.error('主内容容器未找到');
            return;
        }

        console.log('UI组件初始化完成，开始加载默认页面');
        
        // 加载默认页面
        this.navigateToPage(this.currentPage);
    },

    /**
     * 初始化移动端菜单
     */
    initMobileMenu: function() {
        // 创建菜单切换按钮
        const header = Utils.dom.find('.app-header');
        if (header && window.innerWidth <= 768) {
            const menuToggle = document.createElement('button');
            menuToggle.className = 'menu-toggle';
            menuToggle.innerHTML = '☰';
            menuToggle.addEventListener('click', () => {
                Utils.dom.toggleClass(this.elements.sidebar, 'active');
            });

            const headerBrand = Utils.dom.find('.header-brand');
            if (headerBrand) {
                headerBrand.insertBefore(menuToggle, headerBrand.firstChild);
            }
        }

        // 监听窗口大小变化
        window.addEventListener('resize', Utils.debounce(() => {
            if (window.innerWidth > 768) {
                Utils.dom.removeClass(this.elements.sidebar, 'active');
            }
        }, 250));
    },

    /**
     * 显示加载动画
     */
    showLoading: function() {
        if (this.elements.loading) {
            Utils.dom.removeClass(this.elements.loading, 'hidden');
        }
    },

    /**
     * 隐藏加载动画
     */
    hideLoading: function() {
        if (this.elements.loading) {
            Utils.dom.addClass(this.elements.loading, 'hidden');
        }
    },

    /**
     * 设置状态文本
     * @param {string} text - 状态文本
     */
    setStatus: function(text) {
        if (this.elements.statusText) {
            this.elements.statusText.textContent = text;
        }
    },

    /**
     * 设置连接状态
     * @param {boolean} connected - 是否连接
     */
    setConnectionStatus: function(connected) {
        if (this.elements.connectionStatus) {
            this.elements.connectionStatus.textContent = connected ? '已连接' : '未连接';
            this.elements.connectionStatus.className = connected ? 'status-connected' : 'status-disconnected';
        }
    },

    /**
     * 导航到指定页面
     * @param {string} page - 页面名称
     */
    navigateToPage: function(page) {
        this.currentPage = page;
        this.showLoading();

        // 更新面包屑
        this.updateBreadcrumb(page);

        // 加载页面内容
        this.loadPageContent(page);

        // 在移动端关闭侧边栏
        if (window.innerWidth <= 768) {
            Utils.dom.removeClass(this.elements.sidebar, 'active');
        }
    },

    /**
     * 设置活动导航链接
     * @param {Element} activeLink - 活动链接元素
     */
    setActiveNavLink: function(activeLink) {
        // 移除所有活动状态
        this.elements.navLinks.forEach(link => {
            Utils.dom.removeClass(link, 'active');
        });
        
        // 设置当前活动状态
        Utils.dom.addClass(activeLink, 'active');
        
        // 展开父级菜单
        const parentItem = activeLink.closest('.nav-item');
        if (parentItem) {
            Utils.dom.addClass(parentItem, 'active');
        }
    },

    /**
     * 更新面包屑导航
     * @param {string} page - 页面名称
     */
    updateBreadcrumb: function(page) {
        const breadcrumbMap = {
            'network': '网络配置',
            'network-selection': '网络配置 > 网络选择',
            'network-ethernet': '网络配置 > 以太网配置',
            'device': '设备配置',
            'center': '设备配置 > 呼叫中心',
            'gateway': '设备配置 > 互联网关',
            'recorder': '设备配置 > 录音配置',
            'sci': '设备配置 > 基站配置',
            'switch': '设备配置 > 交换配置',
            'system': '系统管理',
            'system-logs': '系统管理 > 系统日志',
            'system-signal': '系统管理 > 信号检测',
            'system-reboot': '系统管理 > 系统重启',
            'system-reset': '系统管理 > 配置重置',
            'ntp': '时间同步',
            'auth': '密码修改'
        };
        
        this.elements.breadcrumb.textContent = breadcrumbMap[page] || page;
    },

    /**
     * 加载页面内容
     * @param {string} page - 页面名称
     */
    loadPageContent: function(page) {
        console.log('开始加载页面内容:', page);
        
        try {
            this.renderPageContent(page);
            this.hideLoading();
            console.log('页面内容加载完成:', page);
        } catch (error) {
            console.error('页面加载失败:', error);
            this.elements.mainContent.innerHTML = `
                <div class="card">
                    <div class="card-body text-center">
                        <h3>页面加载失败</h3>
                        <p class="text-muted">${error.message}</p>
                        <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                    </div>
                </div>
            `;
            this.hideLoading();
        }
    },

    /**
     * 渲染页面内容
     * @param {string} page - 页面名称
     */
    renderPageContent: function(page) {
        let content = '';
        
        switch (page) {
            case 'network-selection':
                content = this.renderNetworkSelectionPage();
                break;
            case 'network-ethernet':
                content = this.renderNetworkEthernetPage();
                break;
            case 'device-config':
                content = this.renderDeviceConfigPage();
                break;
            case 'center':
                content = this.renderCenterConfigPage();
                break;
            case 'gateway':
                content = this.renderGatewayConfigPage();
                break;
            case 'recorder':
                content = this.renderRecorderConfigPage();
                break;
            case 'sci':
                content = this.renderSCIConfigPage();
                break;
            case 'switch':
                content = this.renderSwitchConfigPage();
                break;
            case 'system-logs':
                content = this.renderSystemLogsPage();
                break;
            case 'system-signal':
                content = this.renderSystemSignalPage();
                break;
            case 'system-reboot':
                content = this.renderSystemRebootPage();
                break;
            case 'system-reset':
                content = this.renderSystemResetPage();
                break;
            case 'ntp':
                content = this.renderNTPConfigPage();
                break;
            case 'auth':
                content = this.renderAuthPage();
                break;
            case 'system':
                content = this.renderSystemPage();
                break;
            default:
                content = this.renderDefaultPage();
        }
        
        this.elements.mainContent.innerHTML = content;
        this.bindPageEvents(page);
    },

    /**
     * 绑定页面特定事件
     * @param {string} page - 页面名称
     */
    bindPageEvents: function(page) {
        // 绑定表单提交事件
        const forms = Utils.dom.findAll('form', this.elements.mainContent);
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmit(form, page);
            });
        });

        // 绑定按钮点击事件
        const buttons = Utils.dom.findAll('button', this.elements.mainContent);
        buttons.forEach(button => {
            const action = button.dataset.action;
            if (action) {
                button.addEventListener('click', () => {
                    this.handleButtonAction(action, page);
                });
            }
        });

        // 加载页面数据
        this.loadPageData(page);
    },

    /**
     * 加载页面数据
     * @param {string} page - 页面名称
     */
    loadPageData: function(page) {
        const dataLoaders = {
            'center': () => this.loadCenterConfig(),
            'gateway': () => this.loadGatewayConfig(),
            'recorder': () => this.loadRecorderConfig(),
            'sci': () => this.loadSCIConfig(),
            'switch': () => this.loadSwitchConfig(),
            'ntp': () => this.loadNTPConfig()
        };

        const loader = dataLoaders[page];
        if (loader) {
            loader().catch(error => {
                console.error(`加载${page}页面数据失败:`, error);
                this.showNotification('数据加载失败: ' + error.message, 'warning');
            });
        }
    },

    /**
     * 加载呼叫中心配置
     */
    loadCenterConfig: async function() {
        try {
            const config = await API.device.getCenter();
            this.fillFormData('center-config-form', config);
            this.updateCenterStatus(config);
        } catch (error) {
            console.error('加载呼叫中心配置失败:', error);
        }
    },

    /**
     * 加载网关配置
     */
    loadGatewayConfig: async function() {
        try {
            const config = await API.device.getGateway();
            this.fillFormData('gateway-config-form', config);
        } catch (error) {
            console.error('加载网关配置失败:', error);
        }
    },

    /**
     * 加载录音配置
     */
    loadRecorderConfig: async function() {
        try {
            const config = await API.device.getRecorder();
            this.fillFormData('recorder-config-form', config);
        } catch (error) {
            console.error('加载录音配置失败:', error);
        }
    },

    /**
     * 加载SCI配置
     */
    loadSCIConfig: async function() {
        try {
            const config = await API.device.getSCI();
            this.fillFormData('sci-config-form', config);
        } catch (error) {
            console.error('加载SCI配置失败:', error);
        }
    },

    /**
     * 加载交换机配置
     */
    loadSwitchConfig: async function() {
        try {
            const config = await API.device.getSwitch();
            this.fillFormData('switch-config-form', config);
        } catch (error) {
            console.error('加载交换机配置失败:', error);
        }
    },

    /**
     * 加载NTP配置
     */
    loadNTPConfig: async function() {
        try {
            const config = await API.ntp.get();
            this.fillFormData('ntp-config-form', config);
            this.updateNTPStatus(config);
        } catch (error) {
            console.error('加载NTP配置失败:', error);
        }
    },

    /**
     * 填充表单数据
     * @param {string} formId - 表单ID
     * @param {Object} data - 数据对象
     */
    fillFormData: function(formId, data) {
        const form = document.getElementById(formId);
        if (!form || !data) return;

        Object.keys(data).forEach(key => {
            const element = form.querySelector(`[name="${key}"]`);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = data[key] == 1 || data[key] === true;
                } else if (element.type === 'radio') {
                    const radio = form.querySelector(`[name="${key}"][value="${data[key]}"]`);
                    if (radio) radio.checked = true;
                } else {
                    element.value = data[key] || '';
                }
            }
        });
    },

    /**
     * 更新呼叫中心状态
     * @param {Object} config - 配置数据
     */
    updateCenterStatus: function(config) {
        const statusElement = document.getElementById('center-connection-status');
        const lastConnectElement = document.getElementById('center-last-connect');

        if (statusElement) {
            statusElement.textContent = config.connected ? '已连接' : '未连接';
            statusElement.className = 'status-value ' + (config.connected ? 'text-success' : 'text-danger');
        }

        if (lastConnectElement && config.last_connect) {
            lastConnectElement.textContent = new Date(config.last_connect).toLocaleString();
        }
    },

    /**
     * 更新NTP状态
     * @param {Object} config - 配置数据
     */
    updateNTPStatus: function(config) {
        const systemTimeElement = document.getElementById('system-time');
        const syncStatusElement = document.getElementById('sync-status');
        const lastSyncElement = document.getElementById('last-sync');
        const timeOffsetElement = document.getElementById('time-offset');

        if (systemTimeElement) {
            systemTimeElement.textContent = new Date().toLocaleString();
        }

        if (syncStatusElement) {
            syncStatusElement.textContent = config.synced ? '已同步' : '未同步';
            syncStatusElement.className = 'status-value ' + (config.synced ? 'text-success' : 'text-warning');
        }

        if (lastSyncElement && config.last_sync) {
            lastSyncElement.textContent = new Date(config.last_sync).toLocaleString();
        }

        if (timeOffsetElement && config.time_offset !== undefined) {
            timeOffsetElement.textContent = config.time_offset + 'ms';
        }
    },

    /**
     * 处理表单提交
     * @param {Element} form - 表单元素
     * @param {string} page - 页面名称
     */
    handleFormSubmit: function(form, page) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        this.showLoading();
        
        // 根据页面类型调用相应的API
        this.savePageConfig(page, data)
            .then(() => {
                this.showNotification('配置保存成功', 'success');
            })
            .catch(error => {
                this.showNotification('配置保存失败: ' + error.message, 'error');
            })
            .finally(() => {
                this.hideLoading();
            });
    },

    /**
     * 处理按钮操作
     * @param {string} action - 操作类型
     * @param {string} page - 页面名称
     */
    handleButtonAction: function(action, page) {
        switch (action) {
            case 'reboot':
                this.confirmReboot();
                break;
            case 'reset':
                this.resetForm(page);
                break;
            case 'refresh':
                this.refreshPageData(page);
                break;
            case 'sync-now':
                this.syncNTPNow();
                break;
            default:
                console.log('Unknown action:', action);
        }
    },

    /**
     * 重置表单
     * @param {string} page - 页面名称
     */
    resetForm: function(page) {
        const formIds = {
            'center': 'center-config-form',
            'gateway': 'gateway-config-form',
            'recorder': 'recorder-config-form',
            'sci': 'sci-config-form',
            'switch': 'switch-config-form',
            'ntp': 'ntp-config-form',
            'auth': 'auth-config-form'
        };

        const formId = formIds[page];
        if (formId) {
            const form = document.getElementById(formId);
            if (form) {
                form.reset();
                // 重新加载数据
                this.loadPageData(page);
            }
        }
    },

    /**
     * 立即同步NTP时间
     */
    syncNTPNow: async function() {
        try {
            this.showLoading();
            await API.ntp.syncNow();
            this.showNotification('时间同步成功', 'success');
            // 更新NTP状态
            this.loadNTPConfig();
        } catch (error) {
            this.showNotification('时间同步失败: ' + error.message, 'error');
        } finally {
            this.hideLoading();
        }
    },

    /**
     * 保存页面配置
     * @param {string} page - 页面名称
     * @param {Object} data - 配置数据
     * @returns {Promise} 保存结果
     */
    savePageConfig: async function(page, data) {
        const apiMap = {
            'network-selection': () => API.network.saveSelection(data),
            'network-ethernet': () => API.network.saveEthernet(data),
            'center': () => API.device.saveCenter(data),
            'gateway': () => API.device.saveGateway(data),
            'recorder': () => API.device.saveRecorder(data),
            'sci': () => API.device.saveSCI(data),
            'switch': () => API.device.saveSwitch(data),
            'ntp': () => API.ntp.save(data),
            'auth': () => API.auth.changePassword(data)
        };
        
        const apiCall = apiMap[page];
        if (apiCall) {
            return await apiCall();
        } else {
            throw new Error('未知的页面类型');
        }
    },

    /**
     * 刷新页面数据
     * @param {string} page - 页面名称
     */
    refreshPageData: function(page) {
        this.showLoading();
        this.loadPageContent(page);
    },

    /**
     * 显示模态框
     * @param {string} title - 标题
     * @param {string} content - 内容
     * @param {Function} onConfirm - 确认回调
     */
    showModal: function(title, content, onConfirm = null) {
        if (this.elements.modal) {
            this.elements.modalTitle.textContent = title;
            this.elements.modalBody.innerHTML = content;

            // 设置确认按钮事件
            this.elements.modalConfirm.onclick = () => {
                if (onConfirm) onConfirm();
                this.hideModal();
            };

            Utils.dom.addClass(this.elements.modal, 'show');
        }
    },

    /**
     * 隐藏模态框
     */
    hideModal: function() {
        if (this.elements.modal) {
            Utils.dom.removeClass(this.elements.modal, 'show');
        }
    },

    /**
     * 显示通知
     * @param {string} message - 消息内容
     * @param {string} type - 通知类型 (success, error, warning, info)
     * @param {number} duration - 显示时长(毫秒)
     */
    showNotification: function(message, type = 'info', duration = 3000) {
        if (this.elements.notification) {
            this.elements.notificationText.textContent = message;
            this.elements.notification.className = `notification ${type} show`;

            // 自动隐藏
            setTimeout(() => {
                this.hideNotification();
            }, duration);
        }
    },

    /**
     * 隐藏通知
     */
    hideNotification: function() {
        if (this.elements.notification) {
            Utils.dom.removeClass(this.elements.notification, 'show');
        }
    },

    /**
     * 确认退出登录
     */
    confirmLogout: function() {
        this.showModal(
            '确认退出',
            '您确定要退出系统吗？',
            () => {
                window.location.href = '/login.html';
            }
        );
    },

    /**
     * 确认系统重启
     */
    confirmReboot: function() {
        this.showModal(
            '确认重启',
            '您确定要重启系统吗？重启后需要等待约2分钟才能重新访问。',
            async () => {
                try {
                    await API.system.reboot();
                    this.showNotification('系统重启命令已发送', 'success');
                } catch (error) {
                    this.showNotification('重启失败: ' + error.message, 'error');
                }
            }
        );
    },

    /**
     * 确认配置重置
     */
    confirmReset: function() {
        this.showModal(
            '确认重置',
            '您确定要重置所有配置吗？此操作将恢复出厂设置，无法撤销。',
            async () => {
                try {
                    await API.system.reset();
                    this.showNotification('配置重置成功', 'success');
                } catch (error) {
                    this.showNotification('重置失败: ' + error.message, 'error');
                }
            }
        );
    },

    // 以下方法将在后续扩展中实现具体的页面渲染逻辑
    renderNetworkSelectionPage: function() {
        const content = NetworkSelectionPage.render();
        // 渲染后初始化页面
        setTimeout(() => {
            NetworkSelectionPage.init();
        }, 100);
        return content;
    },

    renderNetworkEthernetPage: function() {
        const content = NetworkEthernetPage.render();
        // 渲染后初始化页面
        setTimeout(() => {
            NetworkEthernetPage.init();
        }, 100);
        return content;
    },

    renderDeviceConfigPage: function() {
        const content = DeviceConfigPage.render();
        // 渲染后初始化页面
        setTimeout(() => {
            DeviceConfigPage.init();
        }, 100);
        return content;
    },

    renderSystemPage: function() {
        const content = SystemManagementPage.render();
        // 渲染后初始化页面
        setTimeout(() => {
            SystemManagementPage.init();
        }, 100);
        return content;
    },

    renderCenterConfigPage: function() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h2>呼叫中心配置</h2>
                    <div class="page-actions">
                        <button type="button" class="btn btn-secondary" data-action="refresh">
                            <i class="icon-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>呼叫中心基本配置</h3>
                            <p class="card-description">配置呼叫中心的基本参数和连接设置</p>
                        </div>

                        <div class="card-body">
                            <form id="center-config-form" class="config-form">
                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label required">服务器IP地址</label>
                                        <input type="text" name="server_ip" class="form-control"
                                               placeholder="例如: ************0" required>
                                        <small class="form-help">呼叫中心服务器的IP地址</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label required">服务器端口</label>
                                        <input type="number" name="server_port" class="form-control"
                                               placeholder="例如: 5060" min="1" max="65535" required>
                                        <small class="form-help">呼叫中心服务器的端口号</small>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label">设备ID</label>
                                        <input type="text" name="device_id" class="form-control"
                                               placeholder="例如: DEV001" maxlength="32">
                                        <small class="form-help">设备在呼叫中心的唯一标识</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">心跳间隔(秒)</label>
                                        <input type="number" name="heartbeat_interval" class="form-control"
                                               placeholder="30" min="10" max="300" value="30">
                                        <small class="form-help">与服务器的心跳检测间隔</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="checkbox-item">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="auto_connect" value="1" checked>
                                            <span class="checkbox-text">启用自动连接</span>
                                        </label>
                                        <small class="form-help">系统启动时自动连接到呼叫中心</small>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="icon-save"></i> 保存配置
                                    </button>
                                    <button type="button" class="btn btn-secondary" data-action="reset">
                                        <i class="icon-reset"></i> 重置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3>连接状态</h3>
                        </div>
                        <div class="card-body">
                            <div class="status-grid" id="center-status">
                                <div class="status-item">
                                    <span class="status-label">连接状态:</span>
                                    <span class="status-value" id="center-connection-status">检测中...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">最后连接:</span>
                                    <span class="status-value" id="center-last-connect">未知</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderGatewayConfigPage: function() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h2>互联网关配置</h2>
                    <div class="page-actions">
                        <button type="button" class="btn btn-secondary" data-action="refresh">
                            <i class="icon-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>网关基本配置</h3>
                            <p class="card-description">配置互联网关的连接参数和路由设置</p>
                        </div>

                        <div class="card-body">
                            <form id="gateway-config-form" class="config-form">
                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label required">网关IP地址</label>
                                        <input type="text" name="gateway_ip" class="form-control"
                                               placeholder="例如: ***********" required>
                                        <small class="form-help">互联网关的IP地址</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label required">网关端口</label>
                                        <input type="number" name="gateway_port" class="form-control"
                                               placeholder="例如: 5060" min="1" max="65535" required>
                                        <small class="form-help">互联网关的端口号</small>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label">用户名</label>
                                        <input type="text" name="username" class="form-control"
                                               placeholder="网关认证用户名" maxlength="64">
                                        <small class="form-help">连接网关的用户名</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">密码</label>
                                        <input type="password" name="password" class="form-control"
                                               placeholder="网关认证密码" maxlength="64">
                                        <small class="form-help">连接网关的密码</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">传输协议</label>
                                    <div class="radio-group">
                                        <label class="radio-item">
                                            <input type="radio" name="protocol" value="udp" checked>
                                            <span class="radio-label">UDP</span>
                                            <small class="form-help">用户数据报协议</small>
                                        </label>
                                        <label class="radio-item">
                                            <input type="radio" name="protocol" value="tcp">
                                            <span class="radio-label">TCP</span>
                                            <small class="form-help">传输控制协议</small>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="checkbox-item">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="enable_gateway" value="1" checked>
                                            <span class="checkbox-text">启用互联网关</span>
                                        </label>
                                        <small class="form-help">启用与互联网关的连接</small>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="icon-save"></i> 保存配置
                                    </button>
                                    <button type="button" class="btn btn-secondary" data-action="reset">
                                        <i class="icon-reset"></i> 重置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderRecorderConfigPage: function() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h2>录音配置</h2>
                    <div class="page-actions">
                        <button type="button" class="btn btn-secondary" data-action="refresh">
                            <i class="icon-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>录音基本设置</h3>
                            <p class="card-description">配置录音功能的基本参数和存储设置</p>
                        </div>

                        <div class="card-body">
                            <form id="recorder-config-form" class="config-form">
                                <div class="form-group">
                                    <div class="checkbox-item">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="enable_recording" value="1">
                                            <span class="checkbox-text">启用录音功能</span>
                                        </label>
                                        <small class="form-help">开启或关闭录音功能</small>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label">录音格式</label>
                                        <select name="audio_format" class="form-control">
                                            <option value="wav">WAV</option>
                                            <option value="mp3">MP3</option>
                                            <option value="aac">AAC</option>
                                        </select>
                                        <small class="form-help">录音文件的音频格式</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">音质设置</label>
                                        <select name="audio_quality" class="form-control">
                                            <option value="low">低质量</option>
                                            <option value="medium" selected>中等质量</option>
                                            <option value="high">高质量</option>
                                        </select>
                                        <small class="form-help">录音的音质级别</small>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label">存储路径</label>
                                        <input type="text" name="storage_path" class="form-control"
                                               placeholder="/var/recordings" value="/var/recordings">
                                        <small class="form-help">录音文件的存储目录</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">最大存储(MB)</label>
                                        <input type="number" name="max_storage" class="form-control"
                                               placeholder="1024" min="100" max="10240" value="1024">
                                        <small class="form-help">录音文件的最大存储空间</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">自动删除策略</label>
                                    <div class="radio-group">
                                        <label class="radio-item">
                                            <input type="radio" name="auto_delete" value="never" checked>
                                            <span class="radio-label">永不删除</span>
                                            <small class="form-help">手动管理录音文件</small>
                                        </label>
                                        <label class="radio-item">
                                            <input type="radio" name="auto_delete" value="7days">
                                            <span class="radio-label">7天后删除</span>
                                            <small class="form-help">自动删除7天前的录音</small>
                                        </label>
                                        <label class="radio-item">
                                            <input type="radio" name="auto_delete" value="30days">
                                            <span class="radio-label">30天后删除</span>
                                            <small class="form-help">自动删除30天前的录音</small>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="icon-save"></i> 保存配置
                                    </button>
                                    <button type="button" class="btn btn-secondary" data-action="reset">
                                        <i class="icon-reset"></i> 重置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderSCIConfigPage: function() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h2>SCI基站配置</h2>
                    <div class="page-actions">
                        <button type="button" class="btn btn-secondary" data-action="refresh">
                            <i class="icon-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>SCI基站基本配置</h3>
                            <p class="card-description">配置SCI基站的连接参数和通信设置</p>
                        </div>

                        <div class="card-body">
                            <form id="sci-config-form" class="config-form">
                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label required">基站IP地址</label>
                                        <input type="text" name="sci_ip" class="form-control"
                                               placeholder="例如: *************" required>
                                        <small class="form-help">SCI基站的IP地址</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label required">基站端口</label>
                                        <input type="number" name="sci_port" class="form-control"
                                               placeholder="例如: 8080" min="1" max="65535" required>
                                        <small class="form-help">SCI基站的端口号</small>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label">基站ID</label>
                                        <input type="text" name="station_id" class="form-control"
                                               placeholder="例如: SCI001" maxlength="32">
                                        <small class="form-help">基站的唯一标识符</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">通信协议版本</label>
                                        <select name="protocol_version" class="form-control">
                                            <option value="v1.0">V1.0</option>
                                            <option value="v2.0" selected>V2.0</option>
                                            <option value="v3.0">V3.0</option>
                                        </select>
                                        <small class="form-help">SCI通信协议版本</small>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label">连接超时(秒)</label>
                                        <input type="number" name="connect_timeout" class="form-control"
                                               placeholder="30" min="5" max="120" value="30">
                                        <small class="form-help">连接基站的超时时间</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">重试次数</label>
                                        <input type="number" name="retry_count" class="form-control"
                                               placeholder="3" min="1" max="10" value="3">
                                        <small class="form-help">连接失败时的重试次数</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="checkbox-item">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="enable_sci" value="1" checked>
                                            <span class="checkbox-text">启用SCI基站</span>
                                        </label>
                                        <small class="form-help">启用与SCI基站的连接</small>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="icon-save"></i> 保存配置
                                    </button>
                                    <button type="button" class="btn btn-secondary" data-action="reset">
                                        <i class="icon-reset"></i> 重置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderSwitchConfigPage: function() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h2>交换机配置</h2>
                    <div class="page-actions">
                        <button type="button" class="btn btn-secondary" data-action="refresh">
                            <i class="icon-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>交换机基本配置</h3>
                            <p class="card-description">配置交换机的基本参数和端口设置</p>
                        </div>

                        <div class="card-body">
                            <form id="switch-config-form" class="config-form">
                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label required">交换机IP地址</label>
                                        <input type="text" name="switch_ip" class="form-control"
                                               placeholder="例如: ************" required>
                                        <small class="form-help">交换机的管理IP地址</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">管理端口</label>
                                        <input type="number" name="mgmt_port" class="form-control"
                                               placeholder="例如: 23" min="1" max="65535" value="23">
                                        <small class="form-help">交换机的管理端口</small>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label">用户名</label>
                                        <input type="text" name="username" class="form-control"
                                               placeholder="管理员用户名" maxlength="32">
                                        <small class="form-help">交换机管理用户名</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">密码</label>
                                        <input type="password" name="password" class="form-control"
                                               placeholder="管理员密码" maxlength="32">
                                        <small class="form-help">交换机管理密码</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="form-label">工作模式</label>
                                    <div class="radio-group">
                                        <label class="radio-item">
                                            <input type="radio" name="work_mode" value="auto" checked>
                                            <span class="radio-label">自动模式</span>
                                            <small class="form-help">自动检测和配置端口</small>
                                        </label>
                                        <label class="radio-item">
                                            <input type="radio" name="work_mode" value="manual">
                                            <span class="radio-label">手动模式</span>
                                            <small class="form-help">手动配置端口参数</small>
                                        </label>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label">VLAN ID</label>
                                        <input type="number" name="vlan_id" class="form-control"
                                               placeholder="1" min="1" max="4094" value="1">
                                        <small class="form-help">默认VLAN标识</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">端口数量</label>
                                        <select name="port_count" class="form-control">
                                            <option value="8">8端口</option>
                                            <option value="16">16端口</option>
                                            <option value="24" selected>24端口</option>
                                            <option value="48">48端口</option>
                                        </select>
                                        <small class="form-help">交换机端口数量</small>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="checkbox-item">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="enable_switch" value="1" checked>
                                            <span class="checkbox-text">启用交换机管理</span>
                                        </label>
                                        <small class="form-help">启用交换机的远程管理功能</small>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="icon-save"></i> 保存配置
                                    </button>
                                    <button type="button" class="btn btn-secondary" data-action="reset">
                                        <i class="icon-reset"></i> 重置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderSystemLogsPage: function() {
        const content = SystemManagementPage.renderLogs ? SystemManagementPage.renderLogs() : this.renderDefaultSystemPage('系统日志');
        setTimeout(() => {
            if (SystemManagementPage.initLogs) SystemManagementPage.initLogs();
        }, 100);
        return content;
    },

    renderSystemSignalPage: function() {
        const content = SystemManagementPage.renderSignal ? SystemManagementPage.renderSignal() : this.renderDefaultSystemPage('信号检测');
        setTimeout(() => {
            if (SystemManagementPage.initSignal) SystemManagementPage.initSignal();
        }, 100);
        return content;
    },

    renderSystemRebootPage: function() {
        const content = SystemManagementPage.renderReboot ? SystemManagementPage.renderReboot() : this.renderDefaultSystemPage('系统重启');
        setTimeout(() => {
            if (SystemManagementPage.initReboot) SystemManagementPage.initReboot();
        }, 100);
        return content;
    },

    renderSystemResetPage: function() {
        const content = SystemManagementPage.renderReset ? SystemManagementPage.renderReset() : this.renderDefaultSystemPage('配置重置');
        setTimeout(() => {
            if (SystemManagementPage.initReset) SystemManagementPage.initReset();
        }, 100);
        return content;
    },

    renderNTPConfigPage: function() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h2>NTP时间同步</h2>
                    <div class="page-actions">
                        <button type="button" class="btn btn-secondary" data-action="refresh">
                            <i class="icon-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>NTP服务器配置</h3>
                            <p class="card-description">配置网络时间协议服务器，确保系统时间准确</p>
                        </div>

                        <div class="card-body">
                            <form id="ntp-config-form" class="config-form">
                                <div class="form-group">
                                    <div class="checkbox-item">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="enable_ntp" value="1" checked>
                                            <span class="checkbox-text">启用NTP时间同步</span>
                                        </label>
                                        <small class="form-help">启用网络时间协议自动同步</small>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label required">主NTP服务器</label>
                                        <input type="text" name="primary_server" class="form-control"
                                               placeholder="例如: pool.ntp.org" required>
                                        <small class="form-help">主要的NTP时间服务器地址</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">备用NTP服务器</label>
                                        <input type="text" name="secondary_server" class="form-control"
                                               placeholder="例如: time.nist.gov">
                                        <small class="form-help">备用的NTP时间服务器地址</small>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-col">
                                        <label class="form-label">同步间隔(分钟)</label>
                                        <select name="sync_interval" class="form-control">
                                            <option value="5">5分钟</option>
                                            <option value="15">15分钟</option>
                                            <option value="30" selected>30分钟</option>
                                            <option value="60">1小时</option>
                                            <option value="360">6小时</option>
                                        </select>
                                        <small class="form-help">自动同步时间的间隔</small>
                                    </div>
                                    <div class="form-col">
                                        <label class="form-label">时区设置</label>
                                        <select name="timezone" class="form-control">
                                            <option value="UTC">UTC (协调世界时)</option>
                                            <option value="Asia/Shanghai" selected>Asia/Shanghai (北京时间)</option>
                                            <option value="Asia/Tokyo">Asia/Tokyo (东京时间)</option>
                                            <option value="Europe/London">Europe/London (伦敦时间)</option>
                                            <option value="America/New_York">America/New_York (纽约时间)</option>
                                        </select>
                                        <small class="form-help">系统使用的时区</small>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="icon-save"></i> 保存配置
                                    </button>
                                    <button type="button" class="btn btn-secondary" data-action="reset">
                                        <i class="icon-reset"></i> 重置
                                    </button>
                                    <button type="button" class="btn btn-warning" data-action="sync-now">
                                        <i class="icon-sync"></i> 立即同步
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3>当前时间状态</h3>
                        </div>
                        <div class="card-body">
                            <div class="status-grid" id="ntp-status">
                                <div class="status-item">
                                    <span class="status-label">系统时间:</span>
                                    <span class="status-value" id="system-time">获取中...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">同步状态:</span>
                                    <span class="status-value" id="sync-status">检测中...</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">最后同步:</span>
                                    <span class="status-value" id="last-sync">未知</span>
                                </div>
                                <div class="status-item">
                                    <span class="status-label">时间偏差:</span>
                                    <span class="status-value" id="time-offset">计算中...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderAuthPage: function() {
        return `
            <div class="page-container">
                <div class="page-header">
                    <h2>密码修改</h2>
                    <div class="page-actions">
                        <button type="button" class="btn btn-secondary" data-action="refresh">
                            <i class="icon-refresh"></i> 刷新
                        </button>
                    </div>
                </div>

                <div class="page-content">
                    <div class="card">
                        <div class="card-header">
                            <h3>修改登录密码</h3>
                            <p class="card-description">修改系统管理员的登录密码，确保系统安全</p>
                        </div>

                        <div class="card-body">
                            <form id="auth-config-form" class="config-form">
                                <div class="form-group">
                                    <label class="form-label required">当前密码</label>
                                    <input type="password" name="current_password" class="form-control"
                                           placeholder="请输入当前密码" required>
                                    <small class="form-help">请输入当前的登录密码进行验证</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label required">新密码</label>
                                    <input type="password" name="new_password" class="form-control"
                                           placeholder="请输入新密码" required minlength="6" maxlength="32">
                                    <small class="form-help">密码长度为6-32个字符，建议包含字母和数字</small>
                                </div>

                                <div class="form-group">
                                    <label class="form-label required">确认新密码</label>
                                    <input type="password" name="confirm_password" class="form-control"
                                           placeholder="请再次输入新密码" required minlength="6" maxlength="32">
                                    <small class="form-help">请再次输入新密码以确认</small>
                                </div>

                                <div class="form-group">
                                    <div class="checkbox-item">
                                        <label class="checkbox-label">
                                            <input type="checkbox" name="force_logout" value="1" checked>
                                            <span class="checkbox-text">修改后强制重新登录</span>
                                        </label>
                                        <small class="form-help">密码修改成功后立即退出当前会话</small>
                                    </div>
                                </div>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="icon-save"></i> 修改密码
                                    </button>
                                    <button type="button" class="btn btn-secondary" data-action="reset">
                                        <i class="icon-reset"></i> 重置
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3>安全提示</h3>
                        </div>
                        <div class="card-body">
                            <ul class="list-unstyled">
                                <li><i class="icon-info text-info"></i> 建议定期更换登录密码</li>
                                <li><i class="icon-info text-info"></i> 密码应包含字母、数字和特殊字符</li>
                                <li><i class="icon-info text-info"></i> 不要使用过于简单或常见的密码</li>
                                <li><i class="icon-info text-info"></i> 请妥善保管您的登录密码</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    },

    renderDefaultSystemPage: function(title) {
        return `<div class="card"><div class="card-header">${title}</div><div class="card-body">${title}页面内容...</div></div>`;
    },

    renderDefaultPage: function() {
        return '<div class="card"><div class="card-header">欢迎</div><div class="card-body">欢迎使用VICTEL IP交换机配置系统</div></div>';
    }
};
