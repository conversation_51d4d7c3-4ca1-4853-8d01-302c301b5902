/**
 * UI管理模块
 * 负责用户界面的交互和状态管理
 * 按照重构设计方案实现现代化前端界面
 */

const UI = {
    // UI元素缓存
    elements: {},

    // 当前页面状态
    currentPage: 'network-selection',

    /**
     * 初始化UI管理器
     */
    init: function() {
        this.cacheElements();
        this.bindEvents();
        this.initializeComponents();
        this.initMobileMenu();
    },

    /**
     * 缓存常用DOM元素
     */
    cacheElements: function() {
        this.elements = {
            // 主要容器
            mainContent: Utils.dom.find('#main-content'),
            loading: Utils.dom.find('#loading'),
            breadcrumb: Utils.dom.find('#breadcrumb-text'),
            statusText: Utils.dom.find('#status-text'),
            connectionStatus: Utils.dom.find('#connection-status'),

            // 导航菜单
            navMenu: Utils.dom.find('#nav-menu'),
            navLinks: Utils.dom.findAll('.nav-link'),
            sidebar: Utils.dom.find('.app-sidebar'),

            // 模态框
            modal: Utils.dom.find('#modal'),
            modalTitle: Utils.dom.find('#modal-title'),
            modalBody: Utils.dom.find('#modal-body'),
            modalClose: Utils.dom.find('#modal-close'),
            modalCancel: Utils.dom.find('#modal-cancel'),
            modalConfirm: Utils.dom.find('#modal-confirm'),

            // 通知
            notification: Utils.dom.find('#notification'),
            notificationText: Utils.dom.find('#notification-text'),
            notificationClose: Utils.dom.find('#notification-close'),

            // 其他
            logoutBtn: Utils.dom.find('#logout-btn')
        };
    },

    /**
     * 绑定事件监听器
     */
    bindEvents: function() {
        // 导航菜单点击事件
        this.elements.navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.dataset.page;
                if (page) {
                    this.navigateToPage(page);
                    this.setActiveNavLink(link);
                }
            });
        });

        // 模态框事件
        if (this.elements.modalClose) {
            this.elements.modalClose.addEventListener('click', () => {
                this.hideModal();
            });
        }

        if (this.elements.modalCancel) {
            this.elements.modalCancel.addEventListener('click', () => {
                this.hideModal();
            });
        }

        if (this.elements.modal) {
            this.elements.modal.addEventListener('click', (e) => {
                if (e.target === this.elements.modal) {
                    this.hideModal();
                }
            });
        }

        // 通知关闭事件
        if (this.elements.notificationClose) {
            this.elements.notificationClose.addEventListener('click', () => {
                this.hideNotification();
            });
        }

        // 退出登录事件
        if (this.elements.logoutBtn) {
            this.elements.logoutBtn.addEventListener('click', () => {
                this.confirmLogout();
            });
        }

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.hideModal();
                this.hideNotification();
            }
        });
    },

    /**
     * 初始化组件
     */
    initializeComponents: function() {
        // 设置初始状态
        this.setStatus('就绪');
        this.setConnectionStatus(true);

        // 检查DOM元素是否正确缓存
        if (!this.elements.mainContent) {
            console.error('主内容容器未找到');
            return;
        }

        console.log('UI组件初始化完成，开始加载默认页面');
        
        // 加载默认页面
        this.navigateToPage(this.currentPage);
    },

    /**
     * 初始化移动端菜单
     */
    initMobileMenu: function() {
        // 创建菜单切换按钮
        const header = Utils.dom.find('.app-header');
        if (header && window.innerWidth <= 768) {
            const menuToggle = document.createElement('button');
            menuToggle.className = 'menu-toggle';
            menuToggle.innerHTML = '☰';
            menuToggle.addEventListener('click', () => {
                Utils.dom.toggleClass(this.elements.sidebar, 'active');
            });

            const headerBrand = Utils.dom.find('.header-brand');
            if (headerBrand) {
                headerBrand.insertBefore(menuToggle, headerBrand.firstChild);
            }
        }

        // 监听窗口大小变化
        window.addEventListener('resize', Utils.debounce(() => {
            if (window.innerWidth > 768) {
                Utils.dom.removeClass(this.elements.sidebar, 'active');
            }
        }, 250));
    },

    /**
     * 显示加载动画
     */
    showLoading: function() {
        if (this.elements.loading) {
            Utils.dom.removeClass(this.elements.loading, 'hidden');
        }
    },

    /**
     * 隐藏加载动画
     */
    hideLoading: function() {
        if (this.elements.loading) {
            Utils.dom.addClass(this.elements.loading, 'hidden');
        }
    },

    /**
     * 设置状态文本
     * @param {string} text - 状态文本
     */
    setStatus: function(text) {
        if (this.elements.statusText) {
            this.elements.statusText.textContent = text;
        }
    },

    /**
     * 设置连接状态
     * @param {boolean} connected - 是否连接
     */
    setConnectionStatus: function(connected) {
        if (this.elements.connectionStatus) {
            this.elements.connectionStatus.textContent = connected ? '已连接' : '未连接';
            this.elements.connectionStatus.className = connected ? 'status-connected' : 'status-disconnected';
        }
    },

    /**
     * 导航到指定页面
     * @param {string} page - 页面名称
     */
    navigateToPage: function(page) {
        this.currentPage = page;
        this.showLoading();

        // 更新面包屑
        this.updateBreadcrumb(page);

        // 加载页面内容
        this.loadPageContent(page);

        // 在移动端关闭侧边栏
        if (window.innerWidth <= 768) {
            Utils.dom.removeClass(this.elements.sidebar, 'active');
        }
    },

    /**
     * 设置活动导航链接
     * @param {Element} activeLink - 活动链接元素
     */
    setActiveNavLink: function(activeLink) {
        // 移除所有活动状态
        this.elements.navLinks.forEach(link => {
            Utils.dom.removeClass(link, 'active');
        });
        
        // 设置当前活动状态
        Utils.dom.addClass(activeLink, 'active');
        
        // 展开父级菜单
        const parentItem = activeLink.closest('.nav-item');
        if (parentItem) {
            Utils.dom.addClass(parentItem, 'active');
        }
    },

    /**
     * 更新面包屑导航
     * @param {string} page - 页面名称
     */
    updateBreadcrumb: function(page) {
        const breadcrumbMap = {
            'network': '网络配置',
            'network-selection': '网络配置 > 网络选择',
            'network-ethernet': '网络配置 > 以太网配置',
            'device': '设备配置',
            'center': '设备配置 > 呼叫中心',
            'gateway': '设备配置 > 互联网关',
            'recorder': '设备配置 > 录音配置',
            'sci': '设备配置 > 基站配置',
            'switch': '设备配置 > 交换配置',
            'system': '系统管理',
            'system-logs': '系统管理 > 系统日志',
            'system-signal': '系统管理 > 信号检测',
            'system-reboot': '系统管理 > 系统重启',
            'system-reset': '系统管理 > 配置重置',
            'ntp': '时间同步',
            'auth': '密码修改'
        };
        
        this.elements.breadcrumb.textContent = breadcrumbMap[page] || page;
    },

    /**
     * 加载页面内容
     * @param {string} page - 页面名称
     */
    loadPageContent: function(page) {
        console.log('开始加载页面内容:', page);
        
        try {
            this.renderPageContent(page);
            this.hideLoading();
            console.log('页面内容加载完成:', page);
        } catch (error) {
            console.error('页面加载失败:', error);
            this.elements.mainContent.innerHTML = `
                <div class="card">
                    <div class="card-body text-center">
                        <h3>页面加载失败</h3>
                        <p class="text-muted">${error.message}</p>
                        <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                    </div>
                </div>
            `;
            this.hideLoading();
        }
    },

    /**
     * 渲染页面内容
     * @param {string} page - 页面名称
     */
    renderPageContent: function(page) {
        let content = '';
        
        switch (page) {
            case 'network-selection':
                content = this.renderNetworkSelectionPage();
                break;
            case 'network-ethernet':
                content = this.renderNetworkEthernetPage();
                break;
            case 'device-config':
                content = this.renderDeviceConfigPage();
                break;
            case 'center':
                content = this.renderCenterConfigPage();
                break;
            case 'gateway':
                content = this.renderGatewayConfigPage();
                break;
            case 'recorder':
                content = this.renderRecorderConfigPage();
                break;
            case 'sci':
                content = this.renderSCIConfigPage();
                break;
            case 'switch':
                content = this.renderSwitchConfigPage();
                break;
            case 'system-logs':
                content = this.renderSystemLogsPage();
                break;
            case 'system-signal':
                content = this.renderSystemSignalPage();
                break;
            case 'system-reboot':
                content = this.renderSystemRebootPage();
                break;
            case 'system-reset':
                content = this.renderSystemResetPage();
                break;
            case 'ntp':
                content = this.renderNTPConfigPage();
                break;
            case 'auth':
                content = this.renderAuthPage();
                break;
            case 'system':
                content = this.renderSystemPage();
                break;
            default:
                content = this.renderDefaultPage();
        }
        
        this.elements.mainContent.innerHTML = content;
        this.bindPageEvents(page);
    },

    /**
     * 绑定页面特定事件
     * @param {string} page - 页面名称
     */
    bindPageEvents: function(page) {
        // 绑定表单提交事件
        const forms = Utils.dom.findAll('form', this.elements.mainContent);
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmit(form, page);
            });
        });
        
        // 绑定按钮点击事件
        const buttons = Utils.dom.findAll('button', this.elements.mainContent);
        buttons.forEach(button => {
            const action = button.dataset.action;
            if (action) {
                button.addEventListener('click', () => {
                    this.handleButtonAction(action, page);
                });
            }
        });
    },

    /**
     * 处理表单提交
     * @param {Element} form - 表单元素
     * @param {string} page - 页面名称
     */
    handleFormSubmit: function(form, page) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        this.showLoading();
        
        // 根据页面类型调用相应的API
        this.savePageConfig(page, data)
            .then(() => {
                this.showNotification('配置保存成功', 'success');
            })
            .catch(error => {
                this.showNotification('配置保存失败: ' + error.message, 'error');
            })
            .finally(() => {
                this.hideLoading();
            });
    },

    /**
     * 处理按钮操作
     * @param {string} action - 操作类型
     * @param {string} page - 页面名称
     */
    handleButtonAction: function(action, page) {
        switch (action) {
            case 'reboot':
                this.confirmReboot();
                break;
            case 'reset':
                this.confirmReset();
                break;
            case 'refresh':
                this.refreshPageData(page);
                break;
            default:
                console.log('Unknown action:', action);
        }
    },

    /**
     * 保存页面配置
     * @param {string} page - 页面名称
     * @param {Object} data - 配置数据
     * @returns {Promise} 保存结果
     */
    savePageConfig: async function(page, data) {
        const apiMap = {
            'network-selection': () => API.network.saveSelection(data),
            'network-ethernet': () => API.network.saveEthernet(data),
            'center': () => API.device.saveCenter(data),
            'gateway': () => API.device.saveGateway(data),
            'recorder': () => API.device.saveRecorder(data),
            'sci': () => API.device.saveSCI(data),
            'switch': () => API.device.saveSwitch(data),
            'ntp': () => API.ntp.save(data),
            'auth': () => API.auth.changePassword(data)
        };
        
        const apiCall = apiMap[page];
        if (apiCall) {
            return await apiCall();
        } else {
            throw new Error('未知的页面类型');
        }
    },

    /**
     * 刷新页面数据
     * @param {string} page - 页面名称
     */
    refreshPageData: function(page) {
        this.showLoading();
        this.loadPageContent(page);
    },

    /**
     * 显示模态框
     * @param {string} title - 标题
     * @param {string} content - 内容
     * @param {Function} onConfirm - 确认回调
     */
    showModal: function(title, content, onConfirm = null) {
        if (this.elements.modal) {
            this.elements.modalTitle.textContent = title;
            this.elements.modalBody.innerHTML = content;

            // 设置确认按钮事件
            this.elements.modalConfirm.onclick = () => {
                if (onConfirm) onConfirm();
                this.hideModal();
            };

            Utils.dom.addClass(this.elements.modal, 'show');
        }
    },

    /**
     * 隐藏模态框
     */
    hideModal: function() {
        if (this.elements.modal) {
            Utils.dom.removeClass(this.elements.modal, 'show');
        }
    },

    /**
     * 显示通知
     * @param {string} message - 消息内容
     * @param {string} type - 通知类型 (success, error, warning, info)
     * @param {number} duration - 显示时长(毫秒)
     */
    showNotification: function(message, type = 'info', duration = 3000) {
        if (this.elements.notification) {
            this.elements.notificationText.textContent = message;
            this.elements.notification.className = `notification ${type} show`;

            // 自动隐藏
            setTimeout(() => {
                this.hideNotification();
            }, duration);
        }
    },

    /**
     * 隐藏通知
     */
    hideNotification: function() {
        if (this.elements.notification) {
            Utils.dom.removeClass(this.elements.notification, 'show');
        }
    },

    /**
     * 确认退出登录
     */
    confirmLogout: function() {
        this.showModal(
            '确认退出',
            '您确定要退出系统吗？',
            () => {
                window.location.href = '/login.html';
            }
        );
    },

    /**
     * 确认系统重启
     */
    confirmReboot: function() {
        this.showModal(
            '确认重启',
            '您确定要重启系统吗？重启后需要等待约2分钟才能重新访问。',
            async () => {
                try {
                    await API.system.reboot();
                    this.showNotification('系统重启命令已发送', 'success');
                } catch (error) {
                    this.showNotification('重启失败: ' + error.message, 'error');
                }
            }
        );
    },

    /**
     * 确认配置重置
     */
    confirmReset: function() {
        this.showModal(
            '确认重置',
            '您确定要重置所有配置吗？此操作将恢复出厂设置，无法撤销。',
            async () => {
                try {
                    await API.system.reset();
                    this.showNotification('配置重置成功', 'success');
                } catch (error) {
                    this.showNotification('重置失败: ' + error.message, 'error');
                }
            }
        );
    },

    // 以下方法将在后续扩展中实现具体的页面渲染逻辑
    renderNetworkSelectionPage: function() {
        const content = NetworkSelectionPage.render();
        // 渲染后初始化页面
        setTimeout(() => {
            NetworkSelectionPage.init();
        }, 100);
        return content;
    },

    renderNetworkEthernetPage: function() {
        const content = NetworkEthernetPage.render();
        // 渲染后初始化页面
        setTimeout(() => {
            NetworkEthernetPage.init();
        }, 100);
        return content;
    },

    renderDeviceConfigPage: function() {
        const content = DeviceConfigPage.render();
        // 渲染后初始化页面
        setTimeout(() => {
            DeviceConfigPage.init();
        }, 100);
        return content;
    },

    renderSystemPage: function() {
        const content = SystemManagementPage.render();
        // 渲染后初始化页面
        setTimeout(() => {
            SystemManagementPage.init();
        }, 100);
        return content;
    },

    renderCenterConfigPage: function() {
        return '<div class="card"><div class="card-header">呼叫中心配置</div><div class="card-body">呼叫中心配置页面内容...</div></div>';
    },

    renderGatewayConfigPage: function() {
        return '<div class="card"><div class="card-header">互联网关配置</div><div class="card-body">互联网关配置页面内容...</div></div>';
    },

    renderRecorderConfigPage: function() {
        return '<div class="card"><div class="card-header">录音配置</div><div class="card-body">录音配置页面内容...</div></div>';
    },

    renderSCIConfigPage: function() {
        return '<div class="card"><div class="card-header">SCI基站配置</div><div class="card-body">SCI基站配置页面内容...</div></div>';
    },

    renderSwitchConfigPage: function() {
        return '<div class="card"><div class="card-header">交换机配置</div><div class="card-body">交换机配置页面内容...</div></div>';
    },

    renderSystemLogsPage: function() {
        const content = SystemManagementPage.renderLogs ? SystemManagementPage.renderLogs() : this.renderDefaultSystemPage('系统日志');
        setTimeout(() => {
            if (SystemManagementPage.initLogs) SystemManagementPage.initLogs();
        }, 100);
        return content;
    },

    renderSystemSignalPage: function() {
        const content = SystemManagementPage.renderSignal ? SystemManagementPage.renderSignal() : this.renderDefaultSystemPage('信号检测');
        setTimeout(() => {
            if (SystemManagementPage.initSignal) SystemManagementPage.initSignal();
        }, 100);
        return content;
    },

    renderSystemRebootPage: function() {
        const content = SystemManagementPage.renderReboot ? SystemManagementPage.renderReboot() : this.renderDefaultSystemPage('系统重启');
        setTimeout(() => {
            if (SystemManagementPage.initReboot) SystemManagementPage.initReboot();
        }, 100);
        return content;
    },

    renderSystemResetPage: function() {
        const content = SystemManagementPage.renderReset ? SystemManagementPage.renderReset() : this.renderDefaultSystemPage('配置重置');
        setTimeout(() => {
            if (SystemManagementPage.initReset) SystemManagementPage.initReset();
        }, 100);
        return content;
    },

    renderNTPConfigPage: function() {
        return '<div class="card"><div class="card-header">NTP时间同步</div><div class="card-body">NTP时间同步配置页面内容...</div></div>';
    },

    renderAuthPage: function() {
        return '<div class="card"><div class="card-header">密码修改</div><div class="card-body">密码修改页面内容...</div></div>';
    },

    renderDefaultSystemPage: function(title) {
        return `<div class="card"><div class="card-header">${title}</div><div class="card-body">${title}页面内容...</div></div>`;
    },

    renderDefaultPage: function() {
        return '<div class="card"><div class="card-header">欢迎</div><div class="card-body">欢迎使用VICTEL IP交换机配置系统</div></div>';
    }
};
