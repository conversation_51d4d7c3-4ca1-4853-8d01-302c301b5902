/* 主应用样式 - 现代化CSS框架 */

/* CSS变量定义 - 现代化配色方案 */
:root {
    /* 主色调 - 现代蓝色系 */
    --primary-color: #2563eb;
    --primary-light: #3b82f6;
    --primary-dark: #1d4ed8;
    
    /* 次要色调 - 优雅灰色系 */
    --secondary-color: #374151;
    --secondary-light: #4b5563;
    --secondary-dark: #1f2937;
    
    /* 背景色 */
    --background-color: #f8fafc;
    --background-secondary: #f1f5f9;
    
    /* 文本色 */
    --text-color: #1e293b;
    --text-secondary: #64748b;
    --text-light: #94a3b8;
    
    /* 边框和分割线 */
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    
    /* 状态颜色 */
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    
    /* 中性色 */
    --light-color: #ffffff;
    --dark-color: #0f172a;
    --white: #ffffff;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 圆角 */
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --border-radius-xl: 16px;
    
    /* 过渡 */
    --transition: all 0.2s ease-in-out;
    --transition-slow: all 0.3s ease-in-out;
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
    font-weight: 400;
    letter-spacing: 0.01em;
}

/* 主布局 - CSS Grid */
.app-layout {
    display: grid;
    grid-template-rows: auto 1fr;
    height: 100vh;
    overflow: hidden;
    background: linear-gradient(135deg, var(--background-color) 0%, var(--background-secondary) 100%);
}

.app-container {
    display: grid;
    grid-template-columns: 280px 1fr;
    height: 100%;
    overflow: hidden;
    gap: 0;
}

/* 顶部导航栏 - 现代化设计 */
.app-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: var(--spacing-md) var(--spacing-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--shadow-md);
    z-index: 1000;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header-brand .logo {
    width: 36px;
    height: 36px;
    filter: brightness(0) invert(1);
    transition: var(--transition);
}

.header-brand .logo:hover {
    transform: scale(1.05);
}

.header-brand h1 {
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.025em;
    background: linear-gradient(45deg, var(--white), rgba(255, 255, 255, 0.8));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* 移动端汉堡菜单按钮 */
.mobile-menu-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--white);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius);
    transition: var(--transition);
    margin-right: var(--spacing-md);
    line-height: 1;
}

.mobile-menu-toggle:hover {
    background-color: rgba(255, 255, 255, 0.15);
    transform: scale(1.05);
}

.mobile-menu-toggle:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

.header-user {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-weight: 500;
}

/* 侧边栏导航 - 现代化设计 */
.app-sidebar {
    background: var(--white);
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-slow);
}

.nav-menu {
    list-style: none;
    padding: var(--spacing-lg) 0;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-xl);
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
    font-weight: 500;
    position: relative;
}

.nav-link:hover {
    background-color: var(--background-secondary);
    border-left-color: var(--primary-light);
    color: var(--primary-color);
    transform: translateX(2px);
}

.nav-link.active {
    background-color: rgba(37, 99, 235, 0.08);
    border-left-color: var(--primary-color);
    color: var(--primary-color);
    font-weight: 600;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    right: var(--spacing-md);
    width: 4px;
    height: 4px;
    background-color: var(--primary-color);
    border-radius: 50%;
}

.nav-link i {
    margin-right: var(--spacing-md);
    width: 18px;
    text-align: center;
    font-size: 1.1rem;
}

.nav-submenu {
    list-style: none;
    background-color: var(--background-secondary);
    display: none;
    border-left: 3px solid var(--border-color);
    margin-left: var(--spacing-xl);
}

.nav-item:hover .nav-submenu,
.nav-item.active .nav-submenu {
    display: block;
    animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-8px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.nav-submenu .nav-link {
    padding-left: var(--spacing-2xl);
    font-size: 0.875rem;
    font-weight: 400;
    color: var(--text-secondary);
}

.nav-submenu .nav-link:hover {
    color: var(--primary-color);
    background-color: var(--background-color);
}

/* 主要内容区域 - 现代化设计 */
.app-main {
    background: var(--background-color);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.breadcrumb {
    background: var(--white);
    padding: var(--spacing-md) var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    box-shadow: var(--shadow-sm);
}

.main-content {
    flex: 1;
    padding: var(--spacing-xl);
    overflow-y: auto;
    background: var(--white);
    margin: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    border: 1px solid var(--border-light);
}

.status-bar {
    background: var(--white);
    padding: var(--spacing-sm) var(--spacing-xl);
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.status-connected {
    color: var(--success-color);
    font-weight: 600;
}

.status-disconnected {
    color: var(--danger-color);
    font-weight: 600;
}

/* 按钮样式 - 现代化设计 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    min-height: 38px;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover:not(:disabled) {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--white);
    color: var(--text-color);
    border-color: var(--border-color);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--background-secondary);
    border-color: var(--text-secondary);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: var(--white);
    box-shadow: var(--shadow-sm);
}

.btn-success:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-1px);
}

.btn-danger {
    background: linear-gradient(135deg, var(--danger-color), #dc2626);
    color: var(--white);
    box-shadow: var(--shadow-sm);
}

.btn-danger:hover:not(:disabled) {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-1px);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color), #d97706);
    color: var(--white);
    box-shadow: var(--shadow-sm);
}

.btn-warning:hover:not(:disabled) {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-1px);
}

/* 加载动画 - 现代化设计 */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
    font-weight: 500;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-md);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 图标 - 现代化设计 */
.icon-network::before { content: "🌐"; }
.icon-device::before { content: "📱"; }
.icon-system::before { content: "⚙️"; }
.icon-time::before { content: "🕐"; }
.icon-auth::before { content: "🔐"; }
