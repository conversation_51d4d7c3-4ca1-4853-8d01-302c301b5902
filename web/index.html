<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VICTEL IP交换机配置系统</title>
    <link rel="stylesheet" href="./css/app.css">
    <link rel="stylesheet" href="./css/components.css">
    <link rel="stylesheet" href="./css/responsive.css">
</head>
<body>
    <div class="app-layout">
        <!-- 顶部导航栏 -->
        <header class="app-header">
            <div class="header-brand">
                <button class="mobile-menu-toggle" aria-label="切换菜单">
                    ☰
                </button>
                <img src="./assets/logo.svg" alt="VICTEL" class="logo">
                <h1>IP交换机配置系统</h1>
            </div>
            <div class="header-user">
                <span id="current-user">管理员</span>
                <button id="logout-btn" class="btn btn-secondary">退出</button>
            </div>
        </header>
        
        <!-- 主要内容区域 -->
        <div class="app-container">
            <!-- 侧边栏导航 -->
            <nav class="app-sidebar">
                <ul class="nav-menu" id="nav-menu">
                    <li class="nav-item">
                        <a href="#" class="nav-link active" data-page="network">
                            <i class="icon-network"></i>
                            <span>网络配置</span>
                        </a>
                        <ul class="nav-submenu">
                            <li><a href="#" class="nav-link" data-page="network-selection">网络选择</a></li>
                            <li><a href="#" class="nav-link" data-page="network-ethernet">以太网配置</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-page="device">
                            <i class="icon-device"></i>
                            <span>设备配置</span>
                        </a>
                        <ul class="nav-submenu">
                            <li><a href="#" class="nav-link" data-page="device-config">设备信息</a></li>
                            <li><a href="#" class="nav-link" data-page="center">呼叫中心</a></li>
                            <li><a href="#" class="nav-link" data-page="gateway">互联网关</a></li>
                            <li><a href="#" class="nav-link" data-page="recorder">录音配置</a></li>
                            <li><a href="#" class="nav-link" data-page="sci">基站配置</a></li>
                            <li><a href="#" class="nav-link" data-page="switch">交换配置</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-page="system">
                            <i class="icon-system"></i>
                            <span>系统管理</span>
                        </a>
                        <ul class="nav-submenu">
                            <li><a href="#" class="nav-link" data-page="system-logs">系统日志</a></li>
                            <li><a href="#" class="nav-link" data-page="system-signal">信号检测</a></li>
                            <li><a href="#" class="nav-link" data-page="system-reboot">系统重启</a></li>
                            <li><a href="#" class="nav-link" data-page="system-reset">配置重置</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-page="ntp">
                            <i class="icon-time"></i>
                            <span>时间同步</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#" class="nav-link" data-page="auth">
                            <i class="icon-auth"></i>
                            <span>密码修改</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <!-- 主要内容区域 -->
            <main class="app-main">
                <!-- 面包屑导航 -->
                <div class="breadcrumb">
                    <span id="breadcrumb-text">网络配置</span>
                </div>
                
                <!-- 页面内容容器 -->
                <div class="main-content" id="main-content">
                    <!-- 动态加载页面内容 -->
                    <div class="loading" id="loading">
                        <div class="spinner"></div>
                        <span>加载中...</span>
                    </div>
                </div>
                
                <!-- 状态栏 -->
                <div class="status-bar">
                    <span id="status-text">就绪</span>
                    <span id="connection-status" class="status-connected">已连接</span>
                </div>
            </main>
        </div>
    </div>
    
    <!-- 模态对话框 -->
    <div class="modal" id="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">提示</h3>
                <button class="modal-close" id="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- 模态内容 -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" id="modal-cancel">取消</button>
                <button class="btn btn-primary" id="modal-confirm">确定</button>
            </div>
        </div>
    </div>
    
    <!-- 通知提示 -->
    <div class="notification" id="notification">
        <div class="notification-content">
            <span id="notification-text"></span>
            <button class="notification-close" id="notification-close">&times;</button>
        </div>
    </div>
    
    <!-- JavaScript文件 -->
    <script src="./js/utils.js"></script>
    <script src="./js/api.js"></script>
    <script src="./js/ui.js"></script>
    <script src="./js/router.js"></script>

    <!-- 页面模块 -->
    <script src="./js/pages/network-selection.js"></script>
    <script src="./js/pages/network-ethernet.js"></script>
    <script src="./js/pages/device-config.js"></script>
    <script src="./js/pages/system-management.js"></script>

    <script src="./js/app.js"></script>
</body>
</html>
