#!/bin/bash

# WebCfg 模块化系统测试脚本
# 支持单元测试、集成测试、API测试和性能测试

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 测试配置
TEST_MODE="all"
TEST_HOST="localhost"
TEST_PORT=8080
TEST_TIMEOUT=30
VERBOSE=false
GENERATE_REPORT=false
CLEANUP=true
PARALLEL=false
TEST_DATA_DIR="$SCRIPT_DIR/data"
TEST_RESULTS_DIR="$SCRIPT_DIR/results"

# 测试程序路径
BUILD_DIR="$PROJECT_ROOT/build/native"
WEBCFG_BINARY="$BUILD_DIR/webcfg"
WEB_DIR="$PROJECT_ROOT/web"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 测试统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0
SKIPPED_TESTS=0

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

log_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "WebCfg 模块化系统测试脚本"
    echo ""
    echo "用法: $0 [选项] [测试类型]"
    echo ""
    echo "测试类型:"
    echo "  all         运行所有测试 [默认]"
    echo "  unit        单元测试"
    echo "  integration 集成测试"
    echo "  api         API接口测试"
    echo "  performance 性能测试"
    echo "  security    安全测试"
    echo "  stress      压力测试"
    echo ""
    echo "选项:"
    echo "  -h, --host=HOST           测试主机地址 [默认: localhost]"
    echo "  -p, --port=PORT           测试端口 [默认: 8080]"
    echo "  -t, --timeout=SEC         请求超时时间 [默认: 30]"
    echo "  -d, --test-data=DIR       测试数据目录 [默认: ./data]"
    echo "  -o, --output=DIR          结果输出目录 [默认: ./results]"
    echo "  -r, --report              生成HTML测试报告"
    echo "  -j, --parallel            并行执行测试"
    echo "  --no-cleanup              测试后不清理临时文件"
    echo "  -v, --verbose             详细输出模式"
    echo "  --help                    显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 运行所有测试"
    echo "  $0 api --host=************* --port=80 # 测试远程API"
    echo "  $0 performance --parallel --report    # 并行性能测试并生成报告"
    echo "  $0 unit --verbose                     # 详细模式单元测试"
    echo ""
    echo "依赖工具:"
    echo "  - curl    (API测试)"
    echo "  - jq      (JSON解析)"
    echo "  - ab      (性能测试)"
    echo "  - nmap    (安全测试)"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--host)
                TEST_HOST="$2"
                shift 2
                ;;
            --host=*)
                TEST_HOST="${1#*=}"
                shift
                ;;
            -p|--port)
                TEST_PORT="$2"
                shift 2
                ;;
            --port=*)
                TEST_PORT="${1#*=}"
                shift
                ;;
            -t|--timeout)
                TEST_TIMEOUT="$2"
                shift 2
                ;;
            --timeout=*)
                TEST_TIMEOUT="${1#*=}"
                shift
                ;;
            -d|--test-data)
                TEST_DATA_DIR="$2"
                shift 2
                ;;
            --test-data=*)
                TEST_DATA_DIR="${1#*=}"
                shift
                ;;
            -o|--output)
                TEST_RESULTS_DIR="$2"
                shift 2
                ;;
            --output=*)
                TEST_RESULTS_DIR="${1#*=}"
                shift
                ;;
            -r|--report)
                GENERATE_REPORT=true
                shift
                ;;
            -j|--parallel)
                PARALLEL=true
                shift
                ;;
            --no-cleanup)
                CLEANUP=false
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            all|unit|integration|api|performance|security|stress)
                TEST_MODE="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                echo "使用 '$0 --help' 查看帮助信息"
                exit 1
                ;;
        esac
    done
}

# 检查依赖工具
check_dependencies() {
    log_info "检查测试依赖工具..."
    
    local missing_tools=()
    
    # 基本工具
    for tool in curl jq; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    # 性能测试工具
    if [[ "$TEST_MODE" =~ ^(all|performance|stress)$ ]]; then
        for tool in ab wrk; do
            if ! command -v "$tool" &> /dev/null; then
                log_warning "性能测试工具 $tool 未安装"
            fi
        done
    fi
    
    # 安全测试工具
    if [[ "$TEST_MODE" =~ ^(all|security)$ ]]; then
        for tool in nmap nikto; do
            if ! command -v "$tool" &> /dev/null; then
                log_warning "安全测试工具 $tool 未安装"
            fi
        done
    fi
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "缺少必需工具: ${missing_tools[*]}"
        log_info "安装命令: sudo apt-get install ${missing_tools[*]}"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 验证参数
validate_params() {
    # 检查端口号
    if ! [[ "$TEST_PORT" =~ ^[1-9][0-9]*$ ]] || [[ "$TEST_PORT" -gt 65535 ]]; then
        log_error "无效的端口号: $TEST_PORT"
        exit 1
    fi
    
    # 检查超时时间
    if ! [[ "$TEST_TIMEOUT" =~ ^[1-9][0-9]*$ ]]; then
        log_error "无效的超时时间: $TEST_TIMEOUT"
        exit 1
    fi
    
    # 检查测试程序
    if [[ ! -f "$WEBCFG_BINARY" ]]; then
        log_error "找不到测试程序: $WEBCFG_BINARY"
        log_info "请先运行构建脚本: scripts/build/build.sh"
        exit 1
    fi
}

# 设置测试环境
setup_test_env() {
    log_info "设置测试环境..."
    
    # 创建必要目录
    mkdir -p "$TEST_DATA_DIR" "$TEST_RESULTS_DIR"
    
    # 创建测试数据文件
    create_test_data
    
    # 启动测试服务器
    start_test_server
    
    log_info "测试环境设置完成"
}

# 创建测试数据
create_test_data() {
    log_debug "创建测试数据..."
    
    # 网络配置测试数据
    cat > "$TEST_DATA_DIR/network_config.json" << 'EOF'
{
    "selection": {
        "mode": "ethernet",
        "priority": ["ethernet", "3g"]
    },
    "ethernet": {
        "enabled": true,
        "dhcp": false,
        "ip": "*************",
        "netmask": "*************",
        "gateway": "***********",
        "dns1": "*******",
        "dns2": "*******"
    }
}
EOF

    # 呼叫中心配置测试数据
    cat > "$TEST_DATA_DIR/center_config.json" << 'EOF'
{
    "server_ip": "*************",
    "server_port": 5060,
    "local_port": 5061,
    "protocol": "UDP",
    "keepalive_interval": 30,
    "max_channels": 32
}
EOF

    # API测试用例
    cat > "$TEST_DATA_DIR/api_tests.json" << 'EOF'
{
    "tests": [
        {
            "name": "获取网络选择配置",
            "method": "GET",
            "url": "/api/v1/config/network/selection",
            "expected_status": 200
        },
        {
            "name": "设置网络选择配置",
            "method": "POST",
            "url": "/api/v1/config/network/selection",
            "data": "@network_config.json",
            "expected_status": 200
        },
        {
            "name": "获取呼叫中心配置",
            "method": "GET",
            "url": "/api/v1/config/center",
            "expected_status": 200
        }
    ]
}
EOF
}

# 启动测试服务器
start_test_server() {
    log_info "启动测试服务器..."
    
    # 检查端口是否被占用
    if netstat -ln | grep -q ":$TEST_PORT "; then
        log_warning "端口 $TEST_PORT 已被占用，尝试停止现有服务"
        pkill -f "webcfg.*port.*$TEST_PORT" || true
        sleep 2
    fi
    
    # 启动服务器
    "$WEBCFG_BINARY" --port="$TEST_PORT" --web-root="$WEB_DIR" --verbose &
    local server_pid=$!
    echo "$server_pid" > "$TEST_RESULTS_DIR/server.pid"
    
    # 等待服务器启动
    log_info "等待服务器启动..."
    local retry_count=0
    while [[ $retry_count -lt 10 ]]; do
        if curl -s "http://$TEST_HOST:$TEST_PORT/api/v1/config/network/selection" > /dev/null 2>&1; then
            log_info "测试服务器已启动 (PID: $server_pid)"
            return 0
        fi
        sleep 1
        ((retry_count++))
    done
    
    log_error "测试服务器启动失败"
    kill "$server_pid" 2>/dev/null || true
    exit 1
}

# 停止测试服务器
stop_test_server() {
    if [[ -f "$TEST_RESULTS_DIR/server.pid" ]]; then
        local server_pid=$(cat "$TEST_RESULTS_DIR/server.pid")
        if kill -0 "$server_pid" 2>/dev/null; then
            log_info "停止测试服务器 (PID: $server_pid)"
            kill "$server_pid"
            wait "$server_pid" 2>/dev/null || true
        fi
        rm -f "$TEST_RESULTS_DIR/server.pid"
    fi
}

# 运行测试用例
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    log_test "运行测试: $test_name"
    ((TOTAL_TESTS++))
    
    local start_time=$(date +%s.%N)
    local result_file="$TEST_RESULTS_DIR/$(echo "$test_name" | tr ' ' '_').result"
    
    if eval "$test_command" > "$result_file" 2>&1; then
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "0")
        log_info "✓ $test_name (${duration}s)"
        ((PASSED_TESTS++))
        echo "PASSED" > "${result_file}.status"
    else
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc -l 2>/dev/null || echo "0")
        log_error "✗ $test_name (${duration}s)"
        ((FAILED_TESTS++))
        echo "FAILED" > "${result_file}.status"
        
        if [[ "$VERBOSE" == "true" ]]; then
            echo "错误详情:"
            cat "$result_file" | head -20
        fi
    fi
}

# 单元测试
run_unit_tests() {
    log_info "运行单元测试..."
    
    # 测试程序基本功能
    run_test "程序帮助信息" "$WEBCFG_BINARY --help"
    run_test "程序版本信息" "$WEBCFG_BINARY --version"
    
    # 测试参数解析
    run_test "无效端口参数" "! $WEBCFG_BINARY --port=99999 2>/dev/null"
    run_test "无效Web目录参数" "! $WEBCFG_BINARY --web-root=/nonexistent 2>/dev/null"
    
    log_info "单元测试完成"
}

# 集成测试
run_integration_tests() {
    log_info "运行集成测试..."
    
    # 测试服务器启动
    run_test "服务器响应测试" "curl -s -f 'http://$TEST_HOST:$TEST_PORT/' > /dev/null"
    
    # 测试静态文件服务
    run_test "静态文件访问" "curl -s -f 'http://$TEST_HOST:$TEST_PORT/index.html' > /dev/null"
    
    # 测试404错误处理
    run_test "404错误处理" "curl -s 'http://$TEST_HOST:$TEST_PORT/nonexistent' | grep -q '404'"
    
    log_info "集成测试完成"
}

# API测试
run_api_tests() {
    log_info "运行API测试..."
    
    local base_url="http://$TEST_HOST:$TEST_PORT"
    
    # 网络配置API测试
    run_test "获取网络选择配置" "curl -s -f '$base_url/api/v1/config/network/selection'"
    run_test "获取以太网配置" "curl -s -f '$base_url/api/v1/config/network/ethernet'"
    
    # 设备配置API测试
    run_test "获取呼叫中心配置" "curl -s -f '$base_url/api/v1/config/center'"
    run_test "获取网关配置" "curl -s -f '$base_url/api/v1/config/gateway'"
    run_test "获取录音配置" "curl -s -f '$base_url/api/v1/config/recorder'"
    run_test "获取SCI配置" "curl -s -f '$base_url/api/v1/config/sci'"
    run_test "获取交换机配置" "curl -s -f '$base_url/api/v1/config/switch'"
    run_test "获取NTP配置" "curl -s -f '$base_url/api/v1/config/ntp'"
    
    # 系统管理API测试
    run_test "获取系统日志" "curl -s -f '$base_url/api/system/logs'"
    run_test "获取信号强度" "curl -s -f '$base_url/api/system/signal'"
    
    # 测试POST请求（注意：这些可能会修改系统配置）
    if [[ "$VERBOSE" == "true" ]]; then
        run_test "设置网络配置" "curl -s -X POST -H 'Content-Type: application/json' -d @'$TEST_DATA_DIR/network_config.json' '$base_url/api/v1/config/network/selection'"
    fi
    
    log_info "API测试完成"
}

# 性能测试
run_performance_tests() {
    log_info "运行性能测试..."
    
    local base_url="http://$TEST_HOST:$TEST_PORT"
    
    # 使用Apache Bench进行压力测试
    if command -v ab &> /dev/null; then
        run_test "并发连接测试" "ab -n 100 -c 10 -q '$base_url/'"
        run_test "API性能测试" "ab -n 50 -c 5 -q '$base_url/api/v1/config/network/selection'"
    else
        log_warning "跳过Apache Bench测试（ab命令未找到）"
        ((SKIPPED_TESTS++))
    fi
    
    # 使用wrk进行高级性能测试
    if command -v wrk &> /dev/null; then
        run_test "高负载测试" "wrk -t4 -c100 -d10s --timeout=5s '$base_url/'"
    else
        log_warning "跳过wrk性能测试（wrk命令未找到）"
        ((SKIPPED_TESTS++))
    fi
    
    # 内存和CPU使用率测试
    run_test "服务器资源监控" "top -b -n1 -p $(cat '$TEST_RESULTS_DIR/server.pid') | grep webcfg"
    
    log_info "性能测试完成"
}

# 安全测试
run_security_tests() {
    log_info "运行安全测试..."
    
    # 端口扫描
    if command -v nmap &> /dev/null; then
        run_test "端口扫描" "nmap -p$TEST_PORT $TEST_HOST | grep -q 'open'"
    else
        log_warning "跳过端口扫描（nmap命令未找到）"
        ((SKIPPED_TESTS++))
    fi
    
    # 测试SQL注入防护
    run_test "SQL注入测试" "curl -s 'http://$TEST_HOST:$TEST_PORT/api/v1/config/network/selection?id=1%27%20OR%20%271%27=%271' | grep -v 'error'"
    
    # 测试XSS防护
    run_test "XSS测试" "curl -s 'http://$TEST_HOST:$TEST_PORT/?test=%3Cscript%3Ealert(1)%3C/script%3E' | grep -v 'script'"
    
    log_info "安全测试完成"
}

# 压力测试
run_stress_tests() {
    log_info "运行压力测试..."
    
    local base_url="http://$TEST_HOST:$TEST_PORT"
    
    # 长时间连接测试
    run_test "长时间连接" "for i in {1..50}; do curl -s '$base_url/' >/dev/null; done"
    
    # 大量并发请求
    if [[ "$PARALLEL" == "true" ]]; then
        run_test "大量并发请求" "seq 1 100 | xargs -n1 -P10 -I{} curl -s '$base_url/' >/dev/null"
    else
        run_test "顺序大量请求" "for i in {1..100}; do curl -s '$base_url/' >/dev/null; done"
    fi
    
    log_info "压力测试完成"
}

# 生成测试报告
generate_report() {
    if [[ "$GENERATE_REPORT" != "true" ]]; then
        return
    fi
    
    log_info "生成测试报告..."
    
    local report_file="$TEST_RESULTS_DIR/test_report.html"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>WebCfg 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { margin: 20px 0; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .passed { background: #d4edda; border: 1px solid #c3e6cb; }
        .failed { background: #f8d7da; border: 1px solid #f5c6cb; }
        .skipped { background: #fff3cd; border: 1px solid #ffeaa7; }
        .stats { display: flex; gap: 20px; }
        .stat-box { text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>WebCfg 模块化系统测试报告</h1>
        <p>生成时间: $timestamp</p>
        <p>测试模式: $TEST_MODE</p>
        <p>测试目标: $TEST_HOST:$TEST_PORT</p>
    </div>
    
    <div class="summary">
        <h2>测试摘要</h2>
        <div class="stats">
            <div class="stat-box">
                <h3>$TOTAL_TESTS</h3>
                <p>总测试数</p>
            </div>
            <div class="stat-box">
                <h3>$PASSED_TESTS</h3>
                <p>通过</p>
            </div>
            <div class="stat-box">
                <h3>$FAILED_TESTS</h3>
                <p>失败</p>
            </div>
            <div class="stat-box">
                <h3>$SKIPPED_TESTS</h3>
                <p>跳过</p>
            </div>
        </div>
    </div>
    
    <div class="details">
        <h2>测试详情</h2>
EOF

    # 添加测试结果详情
    for result_file in "$TEST_RESULTS_DIR"/*.result; do
        if [[ -f "$result_file" ]]; then
            local test_name=$(basename "$result_file" .result | tr '_' ' ')
            local status_file="${result_file}.status"
            local status="unknown"
            
            if [[ -f "$status_file" ]]; then
                status=$(cat "$status_file")
            fi
            
            local css_class=""
            case $status in
                PASSED) css_class="passed" ;;
                FAILED) css_class="failed" ;;
                SKIPPED) css_class="skipped" ;;
            esac
            
            cat >> "$report_file" << EOF
        <div class="test-result $css_class">
            <h4>$test_name</h4>
            <p>状态: $status</p>
            <details>
                <summary>详细输出</summary>
                <pre>$(cat "$result_file" | head -50)</pre>
            </details>
        </div>
EOF
        fi
    done
    
    cat >> "$report_file" << EOF
    </div>
</body>
</html>
EOF

    log_info "测试报告已生成: $report_file"
}

# 清理测试环境
cleanup_test_env() {
    if [[ "$CLEANUP" != "true" ]]; then
        return
    fi
    
    log_info "清理测试环境..."
    
    # 停止测试服务器
    stop_test_server
    
    # 清理临时文件（可选）
    # rm -rf "$TEST_RESULTS_DIR"/*.tmp
    
    log_info "测试环境清理完成"
}

# 显示测试摘要
show_summary() {
    echo ""
    echo "================================"
    echo "测试摘要"
    echo "================================"
    echo "总测试数: $TOTAL_TESTS"
    echo "通过: $PASSED_TESTS"
    echo "失败: $FAILED_TESTS"
    echo "跳过: $SKIPPED_TESTS"
    echo ""
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        log_info "所有测试通过！ ✓"
    else
        log_error "$FAILED_TESTS 个测试失败！ ✗"
        echo "详细信息请查看: $TEST_RESULTS_DIR/"
    fi
    
    if [[ "$GENERATE_REPORT" == "true" ]]; then
        echo "测试报告: $TEST_RESULTS_DIR/test_report.html"
    fi
    
    echo "================================"
}

# 主函数
main() {
    # 解析参数
    parse_arguments "$@"
    
    echo "================================"
    echo "WebCfg 模块化系统测试"
    echo "================================"
    log_info "测试配置:"
    log_info "  模式: $TEST_MODE"
    log_info "  目标: $TEST_HOST:$TEST_PORT"
    log_info "  超时: ${TEST_TIMEOUT}s"
    log_info "  并行: $PARALLEL"
    log_info "  报告: $GENERATE_REPORT"
    echo "--------------------------------"
    
    # 检查依赖和验证参数
    check_dependencies
    validate_params
    
    # 设置测试环境
    setup_test_env
    
    # 运行测试
    case $TEST_MODE in
        all)
            run_unit_tests
            run_integration_tests
            run_api_tests
            run_performance_tests
            run_security_tests
            ;;
        unit)
            run_unit_tests
            ;;
        integration)
            run_integration_tests
            ;;
        api)
            run_api_tests
            ;;
        performance)
            run_performance_tests
            ;;
        security)
            run_security_tests
            ;;
        stress)
            run_stress_tests
            ;;
        *)
            log_error "未知的测试模式: $TEST_MODE"
            exit 1
            ;;
    esac
    
    # 生成报告
    generate_report
    
    # 清理环境
    cleanup_test_env
    
    # 显示摘要
    show_summary
    
    # 返回适当的退出码
    if [[ $FAILED_TESTS -eq 0 ]]; then
        exit 0
    else
        exit 1
    fi
}

# 信号处理
trap cleanup_test_env EXIT INT TERM

# 执行主函数
main "$@"
