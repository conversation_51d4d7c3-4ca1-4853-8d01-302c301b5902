# WebCfg 脚本使用说明

本目录包含WebCfg模块化系统的构建、部署和测试脚本。

## 目录结构

```
scripts/
├── build/              # 构建脚本
│   ├── build.sh        # 优化的构建脚本
│   └── build_all_platforms.sh
├── deploy/             # 部署脚本
│   └── deploy.sh       # 系统部署和服务管理脚本
├── tests/              # 测试脚本
│   └── test.sh         # 全面的测试脚本
└── README.md           # 本说明文档
```

## 1. 构建脚本 (build.sh)

### 功能特性
- 支持多平台交叉编译 (native, am335x, zynq, 2440, ec20)
- 自动管理第三方库依赖
- 并行编译优化
- 详细的构建日志
- 清理和重建选项

### 使用方法

```bash
# 基本用法
./scripts/build/build.sh --help

# 默认构建本地平台
./scripts/build/build.sh

# 指定平台和构建类型
./scripts/build/build.sh --platform=am335x --type=Debug

# 清理并重建
./scripts/build/build.sh --clean --rebuild-deps

# 详细模式并行构建
./scripts/build/build.sh --verbose --jobs=8

# 构建并安装
./scripts/build/build.sh --install

# 构建并创建安装包
./scripts/build/build.sh --package
```

### 常用选项
- `-p, --platform=PLATFORM` : 目标平台 (native|am335x|zynq|2440|ec20)
- `-t, --type=TYPE` : 构建类型 (Debug|Release)
- `-j, --jobs=NUM` : 并行任务数
- `-c, --clean` : 清理构建目录
- `-r, --rebuild-deps` : 重建第三方库
- `-i, --install` : 安装构建结果
- `-k, --package` : 创建安装包
- `-v, --verbose` : 详细输出模式

## 2. 部署脚本 (deploy.sh)

### 功能特性
- systemd服务自动部署
- 用户和权限管理
- 配置文件生成
- 服务控制 (启动/停止/重启)
- 备份和回滚
- 完整卸载

### 使用方法

```bash
# 查看帮助
./scripts/deploy/deploy.sh --help

# 默认安装
sudo ./scripts/deploy/deploy.sh install

# 自定义安装
sudo ./scripts/deploy/deploy.sh --port=80 --target-dir=/usr/local/webcfg install

# 强制安装，跳过确认
sudo ./scripts/deploy/deploy.sh --force install

# 更新系统
sudo ./scripts/deploy/deploy.sh update

# 服务控制
sudo ./scripts/deploy/deploy.sh start
sudo ./scripts/deploy/deploy.sh stop
sudo ./scripts/deploy/deploy.sh restart
sudo ./scripts/deploy/deploy.sh status

# 查看日志
./scripts/deploy/deploy.sh logs

# 卸载系统
sudo ./scripts/deploy/deploy.sh uninstall
```

### 部署模式
- `install` : 安装系统和服务
- `uninstall` : 卸载系统和服务
- `update` : 更新系统文件
- `start/stop/restart` : 服务控制
- `status` : 查看服务状态
- `logs` : 查看服务日志

### 安装后的系统结构
```
/opt/webcfg/              # 程序目录
├── bin/webcfg            # 主程序
└── docs/                 # 文档

/var/www/webcfg/          # Web文件目录
├── index.html
├── css/
└── js/

/etc/webcfg/              # 配置目录
└── webcfg.conf

/var/log/webcfg/          # 日志目录

/etc/systemd/system/      # systemd服务
└── webcfg.service
```

## 3. 测试脚本 (test.sh)

### 功能特性
- 多类型测试支持 (单元/集成/API/性能/安全)
- 自动化测试环境设置
- HTML测试报告生成
- 并行测试执行
- 详细的测试统计

### 使用方法

```bash
# 查看帮助
./scripts/tests/test.sh --help

# 运行所有测试
./scripts/tests/test.sh

# 运行特定类型测试
./scripts/tests/test.sh api
./scripts/tests/test.sh performance

# 测试远程服务器
./scripts/tests/test.sh --host=************* --port=80 api

# 详细模式并生成报告
./scripts/tests/test.sh --verbose --report

# 并行测试
./scripts/tests/test.sh --parallel performance
```

### 测试类型
- `all` : 运行所有测试
- `unit` : 单元测试
- `integration` : 集成测试
- `api` : API接口测试
- `performance` : 性能测试
- `security` : 安全测试
- `stress` : 压力测试

### 测试依赖工具
请确保安装以下工具：
```bash
# Ubuntu/Debian
sudo apt-get install curl jq apache2-utils nmap

# 可选性能测试工具
sudo apt-get install wrk
```

## 4. 完整工作流程示例

### 开发流程
```bash
# 1. 构建项目
./scripts/build/build.sh --verbose

# 2. 运行测试
./scripts/tests/test.sh --report

# 3. 如果测试通过，部署到测试环境
sudo ./scripts/deploy/deploy.sh --port=8080 install

# 4. 启动服务
sudo ./scripts/deploy/deploy.sh start

# 5. 验证部署
./scripts/tests/test.sh api
```

### 生产部署流程
```bash
# 1. 构建发布版本
./scripts/build/build.sh --type=Release --package

# 2. 部署到生产环境
sudo ./scripts/deploy/deploy.sh --port=80 --force install

# 3. 启动服务
sudo ./scripts/deploy/deploy.sh start

# 4. 验证服务
./scripts/deploy/deploy.sh status
```

### 更新流程
```bash
# 1. 重新构建
./scripts/build/build.sh --clean

# 2. 更新部署
sudo ./scripts/deploy/deploy.sh update

# 3. 验证更新
./scripts/tests/test.sh api
```

## 5. 故障排除

### 构建问题
- 检查第三方库依赖：`ls -la cache/third_party/`
- 清理并重建：`./scripts/build/build.sh --clean --rebuild-deps`
- 查看详细日志：`./scripts/build/build.sh --verbose`

### 部署问题
- 检查权限：确保使用 `sudo` 运行部署脚本
- 查看服务状态：`sudo systemctl status webcfg`
- 查看服务日志：`sudo journalctl -u webcfg -f`

### 测试问题
- 确保服务正在运行：`curl http://localhost:8080/`
- 检查端口占用：`netstat -tlnp | grep 8080`
- 安装测试依赖：`sudo apt-get install curl jq`

## 6. 环境变量

可以通过环境变量自定义构建和部署路径：

```bash
# 构建相关
export WEBCFG_BUILD_ROOT="/custom/build"
export WEBCFG_CACHE_ROOT="/custom/cache"
export WEBCFG_INSTALL_ROOT="/custom/install"

# 然后运行构建
./scripts/build/build.sh
```

## 7. 高级用法

### 多平台构建
```bash
# 构建所有支持的平台
for platform in native am335x zynq; do
    ./scripts/build/build.sh --platform=$platform --clean
done
```

### 自动化测试
```bash
# 构建并测试的自动化脚本
./scripts/build/build.sh && \
./scripts/tests/test.sh --report && \
echo "构建和测试完成！"
```

### 性能监控
```bash
# 部署后持续监控性能
./scripts/deploy/deploy.sh start
while true; do
    ./scripts/tests/test.sh performance
    sleep 300  # 每5分钟测试一次
done
```

## 8. 注意事项

1. **权限要求**：部署脚本需要root权限
2. **端口占用**：确保目标端口未被占用
3. **依赖检查**：运行前会自动检查所需工具
4. **备份策略**：部署脚本会自动备份现有文件
5. **日志记录**：所有操作都有详细日志记录

## 9. 技术支持

如果遇到问题，请：
1. 查看相关脚本的 `--help` 输出
2. 使用 `--verbose` 选项获取详细信息
3. 检查日志文件和错误输出
4. 确认系统依赖和权限设置

---

更多详细信息请参考各脚本的内置帮助文档。 