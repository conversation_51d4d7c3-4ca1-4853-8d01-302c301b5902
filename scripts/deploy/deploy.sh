#!/bin/bash

# WebCfg 模块化系统部署脚本
# 支持systemd服务部署、配置管理和服务控制

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# 默认配置
DEPLOY_MODE="install"
TARGET_DIR="/opt/webcfg"
SERVICE_NAME="webcfg"
SERVICE_USER="webcfg"
SERVICE_GROUP="webcfg"
WEB_PORT=8080
WEB_ROOT="/var/www/webcfg"
CONFIG_DIR="/etc/webcfg"
LOG_DIR="/var/log/webcfg"
SYSTEMD_PATH="/etc/systemd/system"
FORCE=false
BACKUP=true
VERBOSE=false

# 源文件路径
BUILD_DIR="$PROJECT_ROOT/build/native"
SOURCE_WEB_DIR="$PROJECT_ROOT/web"
SOURCE_DOCS_DIR="$PROJECT_ROOT/docs"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 显示帮助信息
show_help() {
    echo "WebCfg 模块化系统部署脚本"
    echo ""
    echo "用法: $0 [选项] [模式]"
    echo ""
    echo "模式:"
    echo "  install     安装系统和服务 [默认]"
    echo "  uninstall   卸载系统和服务"
    echo "  update      更新系统文件"
    echo "  start       启动服务"
    echo "  stop        停止服务"
    echo "  restart     重启服务"
    echo "  status      查看服务状态"
    echo "  logs        查看服务日志"
    echo ""
    echo "选项:"
    echo "  -d, --target-dir=DIR      目标安装目录 [默认: /opt/webcfg]"
    echo "  -u, --user=USER           服务运行用户 [默认: webcfg]"
    echo "  -g, --group=GROUP         服务运行用户组 [默认: webcfg]"
    echo "  -p, --port=PORT           Web服务端口 [默认: 8080]"
    echo "  -w, --web-dir=DIR         Web文件目录 [默认: /var/www/webcfg]"
    echo "  -c, --config-dir=DIR      配置文件目录 [默认: /etc/webcfg]"
    echo "  -l, --log-dir=DIR         日志文件目录 [默认: /var/log/webcfg]"
    echo "  -n, --service-name=NAME   服务名称 [默认: webcfg]"
    echo "  -f, --force               强制执行，跳过确认"
    echo "  --no-backup               不创建备份"
    echo "  -v, --verbose             详细输出模式"
    echo "  -h, --help                显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 install                        # 默认安装"
    echo "  $0 --port=80 --force install      # 指定端口强制安装"
    echo "  $0 --target-dir=/usr/local/webcfg # 自定义安装目录"
    echo "  $0 uninstall                      # 卸载"
    echo "  $0 restart                        # 重启服务"
    echo ""
    echo "注意:"
    echo "  - 安装需要root权限"
    echo "  - 会自动创建systemd服务"
    echo "  - 默认会创建专用用户和用户组"
}

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -d|--target-dir)
                TARGET_DIR="$2"
                shift 2
                ;;
            --target-dir=*)
                TARGET_DIR="${1#*=}"
                shift
                ;;
            -u|--user)
                SERVICE_USER="$2"
                shift 2
                ;;
            --user=*)
                SERVICE_USER="${1#*=}"
                shift
                ;;
            -g|--group)
                SERVICE_GROUP="$2"
                shift 2
                ;;
            --group=*)
                SERVICE_GROUP="${1#*=}"
                shift
                ;;
            -p|--port)
                WEB_PORT="$2"
                shift 2
                ;;
            --port=*)
                WEB_PORT="${1#*=}"
                shift
                ;;
            -w|--web-dir)
                WEB_ROOT="$2"
                shift 2
                ;;
            --web-dir=*)
                WEB_ROOT="${1#*=}"
                shift
                ;;
            -c|--config-dir)
                CONFIG_DIR="$2"
                shift 2
                ;;
            --config-dir=*)
                CONFIG_DIR="${1#*=}"
                shift
                ;;
            -l|--log-dir)
                LOG_DIR="$2"
                shift 2
                ;;
            --log-dir=*)
                LOG_DIR="${1#*=}"
                shift
                ;;
            -n|--service-name)
                SERVICE_NAME="$2"
                shift 2
                ;;
            --service-name=*)
                SERVICE_NAME="${1#*=}"
                shift
                ;;
            -f|--force)
                FORCE=true
                shift
                ;;
            --no-backup)
                BACKUP=false
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            install|uninstall|update|start|stop|restart|status|logs)
                DEPLOY_MODE="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                echo "使用 '$0 --help' 查看帮助信息"
                exit 1
                ;;
        esac
    done
}

# 检查权限
check_permissions() {
    case $DEPLOY_MODE in
        install|uninstall|update|start|stop|restart)
            if [[ $EUID -ne 0 ]]; then
                log_error "此操作需要root权限，请使用sudo运行"
                exit 1
            fi
            ;;
        status|logs)
            # 查看状态和日志不需要root权限
            ;;
    esac
}

# 验证参数
validate_params() {
    # 检查端口号
    if ! [[ "$WEB_PORT" =~ ^[1-9][0-9]*$ ]] || [[ "$WEB_PORT" -gt 65535 ]]; then
        log_error "无效的端口号: $WEB_PORT"
        exit 1
    fi
    
    # 检查源文件
    if [[ "$DEPLOY_MODE" =~ ^(install|update)$ ]]; then
        if [[ ! -f "$BUILD_DIR/webcfg" ]]; then
            log_error "找不到可执行文件: $BUILD_DIR/webcfg"
            log_info "请先运行构建脚本: scripts/build/build.sh"
            exit 1
        fi
        
        if [[ ! -d "$SOURCE_WEB_DIR" ]]; then
            log_error "找不到Web文件目录: $SOURCE_WEB_DIR"
            exit 1
        fi
    fi
}

# 创建用户和用户组
create_user() {
    log_info "创建服务用户和用户组..."
    
    # 创建用户组
    if ! getent group "$SERVICE_GROUP" > /dev/null 2>&1; then
        groupadd --system "$SERVICE_GROUP"
        log_info "已创建用户组: $SERVICE_GROUP"
    else
        log_debug "用户组已存在: $SERVICE_GROUP"
    fi
    
    # 创建用户
    if ! getent passwd "$SERVICE_USER" > /dev/null 2>&1; then
        useradd --system --gid "$SERVICE_GROUP" \
                --home-dir "$TARGET_DIR" \
                --shell /sbin/nologin \
                --comment "WebCfg Service User" \
                "$SERVICE_USER"
        log_info "已创建用户: $SERVICE_USER"
    else
        log_debug "用户已存在: $SERVICE_USER"
    fi
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    local dirs=(
        "$TARGET_DIR"
        "$TARGET_DIR/bin"
        "$WEB_ROOT"
        "$CONFIG_DIR"
        "$LOG_DIR"
    )
    
    for dir in "${dirs[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_debug "已创建目录: $dir"
        fi
    done
    
    # 设置权限
    chown -R "$SERVICE_USER:$SERVICE_GROUP" "$TARGET_DIR" "$WEB_ROOT" "$CONFIG_DIR" "$LOG_DIR"
    chmod 755 "$TARGET_DIR" "$WEB_ROOT" "$CONFIG_DIR"
    chmod 750 "$LOG_DIR"
}

# 安装文件
install_files() {
    log_info "安装程序文件..."
    
    # 备份现有文件
    if [[ "$BACKUP" == "true" && -f "$TARGET_DIR/bin/webcfg" ]]; then
        local backup_file="$TARGET_DIR/bin/webcfg.backup.$(date +%Y%m%d-%H%M%S)"
        cp "$TARGET_DIR/bin/webcfg" "$backup_file"
        log_info "已备份现有程序: $backup_file"
    fi
    
    # 复制可执行文件
    cp "$BUILD_DIR/webcfg" "$TARGET_DIR/bin/"
    chown "$SERVICE_USER:$SERVICE_GROUP" "$TARGET_DIR/bin/webcfg"
    chmod 755 "$TARGET_DIR/bin/webcfg"
    
    # 复制Web文件
    if [[ "$BACKUP" == "true" && -d "$WEB_ROOT/index.html" ]]; then
        local backup_dir="$WEB_ROOT.backup.$(date +%Y%m%d-%H%M%S)"
        cp -r "$WEB_ROOT" "$backup_dir"
        log_info "已备份Web文件: $backup_dir"
    fi
    
    cp -r "$SOURCE_WEB_DIR"/* "$WEB_ROOT/"
    chown -R "$SERVICE_USER:$SERVICE_GROUP" "$WEB_ROOT"
    find "$WEB_ROOT" -type f -exec chmod 644 {} \;
    find "$WEB_ROOT" -type d -exec chmod 755 {} \;
    
    # 复制文档（如果存在）
    if [[ -d "$SOURCE_DOCS_DIR" ]]; then
        cp -r "$SOURCE_DOCS_DIR" "$TARGET_DIR/"
        chown -R "$SERVICE_USER:$SERVICE_GROUP" "$TARGET_DIR/docs"
    fi
    
    log_info "文件安装完成"
}

# 创建配置文件
create_config() {
    log_info "创建配置文件..."
    
    # 创建主配置文件
    cat > "$CONFIG_DIR/webcfg.conf" << EOF
# WebCfg 模块化系统配置文件
# 此文件在服务启动时加载

# 网络配置
port=$WEB_PORT
web_root=$WEB_ROOT
max_connections=100
timeout_seconds=30

# 日志配置
log_level=info
log_file=$LOG_DIR/webcfg.log

# 安全配置
enable_ssl=false
# ssl_cert_file=/etc/ssl/certs/webcfg.crt
# ssl_key_file=/etc/ssl/private/webcfg.key

# 认证配置
require_auth=true
session_timeout=3600

# 模块配置
enable_network_config=true
enable_center_config=true
enable_gateway_config=true
enable_recorder_config=true
enable_sci_config=true
enable_switch_config=true
enable_ntp_config=true
enable_system_mgmt=true
EOF

    chown "$SERVICE_USER:$SERVICE_GROUP" "$CONFIG_DIR/webcfg.conf"
    chmod 640 "$CONFIG_DIR/webcfg.conf"
}

# 创建systemd服务文件
create_systemd_service() {
    log_info "创建systemd服务..."
    
    local service_file="$SYSTEMD_PATH/${SERVICE_NAME}.service"
    
    cat > "$service_file" << EOF
[Unit]
Description=WebCfg Modular Configuration System
Documentation=file://$TARGET_DIR/docs
After=network.target

[Service]
Type=simple
User=$SERVICE_USER
Group=$SERVICE_GROUP
WorkingDirectory=$TARGET_DIR
ExecStart=$TARGET_DIR/bin/webcfg --port=$WEB_PORT --web-root=$WEB_ROOT --config=$CONFIG_DIR/webcfg.conf --verbose
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$TARGET_DIR $WEB_ROOT $CONFIG_DIR $LOG_DIR

# 网络设置
AmbientCapabilities=CAP_NET_BIND_SERVICE

[Install]
WantedBy=multi-user.target
EOF

    chmod 644 "$service_file"
    
    # 重新加载systemd配置
    systemctl daemon-reload
    log_info "systemd服务已创建: $service_file"
}

# 安装系统
install_system() {
    log_info "开始安装WebCfg系统..."
    
    if [[ "$FORCE" != "true" ]]; then
        echo "即将安装WebCfg系统到: $TARGET_DIR"
        echo "服务端口: $WEB_PORT"
        echo "服务用户: $SERVICE_USER"
        read -p "是否继续? [y/N] " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "安装已取消"
            exit 0
        fi
    fi
    
    create_user
    create_directories
    install_files
    create_config
    create_systemd_service
    
    # 启用服务
    systemctl enable "$SERVICE_NAME"
    log_info "服务已启用，开机自启动"
    
    log_info "WebCfg系统安装完成！"
    log_info "使用以下命令管理服务:"
    log_info "  启动: sudo systemctl start $SERVICE_NAME"
    log_info "  停止: sudo systemctl stop $SERVICE_NAME"
    log_info "  重启: sudo systemctl restart $SERVICE_NAME"
    log_info "  状态: sudo systemctl status $SERVICE_NAME"
    log_info "  日志: sudo journalctl -u $SERVICE_NAME -f"
    log_info ""
    log_info "Web界面: http://localhost:$WEB_PORT"
}

# 卸载系统
uninstall_system() {
    log_info "开始卸载WebCfg系统..."
    
    if [[ "$FORCE" != "true" ]]; then
        echo "即将卸载WebCfg系统，删除以下内容:"
        echo "  - 服务: $SERVICE_NAME"
        echo "  - 程序目录: $TARGET_DIR"
        echo "  - Web目录: $WEB_ROOT"
        echo "  - 配置目录: $CONFIG_DIR"
        echo "  - 日志目录: $LOG_DIR"
        echo "  - 用户: $SERVICE_USER"
        read -p "是否继续? [y/N] " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "卸载已取消"
            exit 0
        fi
    fi
    
    # 停止并禁用服务
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        systemctl stop "$SERVICE_NAME"
        log_info "服务已停止"
    fi
    
    if systemctl is-enabled --quiet "$SERVICE_NAME"; then
        systemctl disable "$SERVICE_NAME"
        log_info "服务已禁用"
    fi
    
    # 删除服务文件
    local service_file="$SYSTEMD_PATH/${SERVICE_NAME}.service"
    if [[ -f "$service_file" ]]; then
        rm -f "$service_file"
        systemctl daemon-reload
        log_info "systemd服务文件已删除"
    fi
    
    # 删除文件和目录
    local dirs_to_remove=("$TARGET_DIR" "$WEB_ROOT" "$CONFIG_DIR" "$LOG_DIR")
    for dir in "${dirs_to_remove[@]}"; do
        if [[ -d "$dir" ]]; then
            rm -rf "$dir"
            log_info "已删除目录: $dir"
        fi
    done
    
    # 删除用户和用户组
    if getent passwd "$SERVICE_USER" > /dev/null 2>&1; then
        userdel "$SERVICE_USER"
        log_info "已删除用户: $SERVICE_USER"
    fi
    
    if getent group "$SERVICE_GROUP" > /dev/null 2>&1; then
        groupdel "$SERVICE_GROUP"
        log_info "已删除用户组: $SERVICE_GROUP"
    fi
    
    log_info "WebCfg系统卸载完成"
}

# 更新系统
update_system() {
    log_info "开始更新WebCfg系统..."
    
    # 停止服务
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        systemctl stop "$SERVICE_NAME"
        log_info "服务已停止"
    fi
    
    # 安装文件
    install_files
    
    # 重启服务
    systemctl start "$SERVICE_NAME"
    log_info "服务已重启"
    
    log_info "WebCfg系统更新完成"
}

# 服务控制
service_control() {
    case $DEPLOY_MODE in
        start)
            log_info "启动服务..."
            systemctl start "$SERVICE_NAME"
            ;;
        stop)
            log_info "停止服务..."
            systemctl stop "$SERVICE_NAME"
            ;;
        restart)
            log_info "重启服务..."
            systemctl restart "$SERVICE_NAME"
            ;;
        status)
            systemctl status "$SERVICE_NAME"
            ;;
        logs)
            journalctl -u "$SERVICE_NAME" -f
            ;;
    esac
}

# 主函数
main() {
    # 解析参数
    parse_arguments "$@"
    
    log_debug "部署参数:"
    log_debug "  模式: $DEPLOY_MODE"
    log_debug "  目标目录: $TARGET_DIR"
    log_debug "  服务名称: $SERVICE_NAME"
    log_debug "  服务用户: $SERVICE_USER:$SERVICE_GROUP"
    log_debug "  Web端口: $WEB_PORT"
    log_debug "  Web目录: $WEB_ROOT"
    log_debug "  配置目录: $CONFIG_DIR"
    log_debug "  日志目录: $LOG_DIR"
    
    # 检查权限
    check_permissions
    
    # 验证参数
    validate_params
    
    # 执行操作
    case $DEPLOY_MODE in
        install)
            install_system
            ;;
        uninstall)
            uninstall_system
            ;;
        update)
            update_system
            ;;
        start|stop|restart|status|logs)
            service_control
            ;;
        *)
            log_error "未知的部署模式: $DEPLOY_MODE"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
